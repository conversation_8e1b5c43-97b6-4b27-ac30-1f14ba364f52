#!/usr/bin/env python3
# -*- coding:utf-8 -*-

################################################################################
# File:    d:\Code\Python\alan\2nd\install_dependencies.py
# Project: d:\Code\Python\alan\2nd
# Created Date: Sunday, June 22nd 2025, 20:30:00
# Author: Jeremy
# ------------------------------------------------------------------------------
# Last Modified: Sunday, June 22nd 2025, 20:30:00
# Modified By: Jeremy
# ------------------------------------------------------------------------------
# Copyright (c)   2025 None
# ---------------- ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ----------------
# HISTORY: 
# Date                	By             	Comments
# --------------------	---------------	---------------------------------------
################################################################################

import os
import sys
import subprocess
import platform

def install_package(package):
    """Install a Python package using pip"""
    print(f"Installing {package}...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"Successfully installed {package}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"Failed to install {package}: {e}")
        return False

def main():
    """Install all required dependencies for MyBro browser"""
    print("Installing dependencies for MyBro browser...")
    
    # Required packages
    required_packages = [
        "PySide6",
        "loguru",
        "beautifulsoup4",
        "jsbeautifier",
        "PyQt6-QScintilla"
    ]
    
    # Optional packages based on platform
    if platform.system() == "Windows":
        required_packages.append("pywin32")
    
    # Install each package
    for package in required_packages:
        install_package(package)
    
    # Try to install pytidylib (which requires tidy to be installed)
    try:
        install_package("pytidylib")
    except:
        print("Note: pytidylib installation may require the HTML Tidy library to be installed on your system.")
        print("You can still use the browser without it.")
    
    print("\nDependency installation completed.")
    print("\nYou can now run the MyBro browser with: python mybro.py")

if __name__ == "__main__":
    main()
2025-06-22 06:55:27.709 | INFO     | __main__:main:949 - Application starting
2025-06-22 06:55:28.059 | INFO     | __main__:initenv:140 - Environment initialized
2025-06-22 06:55:31.513 | INFO     | __main__:setup_ui:282 - UI setup complete
2025-06-22 06:55:31.513 | INFO     | __main__:setup_keyboard_shortcuts:658 - Keyboard shortcuts initialized
2025-06-22 06:55:46.475 | WARNING  | __main__:navigate_to:163 - Browser not initialized yet
2025-06-22 06:55:54.323 | WARNING  | __main__:navigate_to:163 - Browser not initialized yet
2025-06-22 06:59:14.079 | INFO     | __main__:main:998 - Application starting
2025-06-22 06:59:14.121 | INFO     | __main__:initenv:160 - Environment initialized
2025-06-22 06:59:14.586 | INFO     | __main__:setup_ui:302 - UI setup complete
2025-06-22 06:59:14.586 | INFO     | __main__:setup_keyboard_shortcuts:685 - Keyboard shortcuts initialized
2025-06-22 06:59:14.607 | ERROR    | __main__:initialize_browser:351 - Failed to initialize browser: wrong # args: should be ".!frame.!frame2.!canvas lower tagOrId ?belowThis?"
2025-06-22 07:35:46.374 | INFO     | __main__:main:874 - Application starting
2025-06-22 07:35:46.417 | INFO     | __main__:initenv:140 - Environment initialized
2025-06-22 07:35:46.832 | INFO     | __main__:setup_ui:282 - UI setup complete
2025-06-22 07:35:46.832 | INFO     | __main__:setup_keyboard_shortcuts:562 - Keyboard shortcuts configured
2025-06-22 07:35:46.859 | ERROR    | __main__:setup_analayer:838 - Error setting up analysis layer: 'MyBro' object has no attribute 'on_drag'
2025-06-22 07:35:46.860 | ERROR    | __main__:_start_webview:737 - Failed to start webview: pywebview must be run on a main thread.
2025-06-22 07:35:46.861 | INFO     | __main__:initialize_browser:784 - Browser initialized successfully
2025-06-22 07:41:58.953 | INFO     | __main__:main:910 - Application starting
2025-06-22 07:41:59.000 | INFO     | __main__:initenv:140 - Environment initialized
2025-06-22 07:41:59.433 | INFO     | __main__:setup_ui:282 - UI setup complete
2025-06-22 07:41:59.449 | INFO     | __main__:setup_keyboard_shortcuts:562 - Keyboard shortcuts configured
2025-06-22 07:41:59.470 | INFO     | __main__:setup_analayer:872 - Analysis layer initialized
2025-06-22 07:41:59.470 | INFO     | __main__:initialize_browser:814 - Browser initialized successfully
2025-06-22 08:56:37.593 | INFO     | __main__:main:192 - Application starting
2025-06-22 08:56:37.594 | INFO     | __main__:init_environment:81 - Environment initialized
2025-06-22 08:56:37.594 | INFO     | __main__:start:96 - Starting browser
2025-06-22 08:57:03.594 | INFO     | __main__:main:195 - Application exited
2025-06-22 08:59:03.219 | INFO     | __main__:main:247 - Application starting
2025-06-22 08:59:03.220 | INFO     | __main__:init_environment:87 - Environment initialized
2025-06-22 08:59:03.220 | INFO     | __main__:start:102 - Starting browser
2025-06-22 08:59:10.383 | INFO     | __main__:main:250 - Application exited
2025-06-22 08:59:18.635 | INFO     | __main__:main:247 - Application starting
2025-06-22 08:59:18.636 | INFO     | __main__:init_environment:87 - Environment initialized
2025-06-22 08:59:18.637 | INFO     | __main__:start:102 - Starting browser
2025-06-22 08:59:40.029 | INFO     | __main__:main:250 - Application exited
2025-06-22 09:03:43.849 | INFO     | __main__:<module>:46 - Using pywebview unknown
2025-06-22 09:03:43.849 | INFO     | __main__:main:278 - Application starting
2025-06-22 09:04:10.018 | INFO     | __main__:<module>:46 - Using pywebview unknown
2025-06-22 09:04:10.018 | INFO     | __main__:main:278 - Application starting
2025-06-22 09:05:31.955 | INFO     | __main__:<module>:46 - Using pywebview unknown
2025-06-22 09:05:31.955 | INFO     | __main__:main:278 - Application starting
2025-06-22 09:05:33.053 | ERROR    | __main__:initialize_browser:173 - Error initializing browser: Python version not supported: 3.12.10 (tags/v3.12.10:0cc8128, Apr  8 2025, 12:21:36) [MSC v.1943 64 bit (AMD64)]
2025-06-22 09:05:57.429 | INFO     | __main__:on_close:267 - Closing browser
2025-06-22 09:07:30.132 | INFO     | __main__:<module>:52 - Using pywebview unknown
2025-06-22 09:07:30.132 | INFO     | __main__:main:337 - Application starting
2025-06-22 09:15:17.170 | INFO     | __main__:<module>:52 - Using pywebview unknown
2025-06-22 09:15:17.171 | INFO     | __main__:main:345 - Application starting
2025-06-22 09:15:18.167 | ERROR    | __main__:initialize_browser:196 - Error initializing browser: Python version not supported: 3.12.10 (tags/v3.12.10:0cc8128, Apr  8 2025, 12:21:36) [MSC v.1943 64 bit (AMD64)]
2025-06-22T15:58:19.989627+1000 - __main__ - 490 - DEBUG - 19932 - 19832 - mybro - tabChanged -  - Switched to tab 0: https://www.google.com
2025-06-22T15:58:19.989627+1000 - __main__ - 237 - INFO - 19932 - 19832 - mybro - addBrowserTab -  - New tab created (index: 0)
2025-06-22T15:58:19.990624+1000 - __main__ - 223 - INFO - 19932 - 19832 - mybro - __init__ -  - Browser initialized
2025-06-22T15:58:21.201269+1000 - __main__ - 113 - INFO - 19932 - 19832 - mybro - handleLoadFinished -  - Page loaded: https://www.google.com/
2025-06-22T15:58:44.259107+1000 - __main__ - 297 - INFO - 19932 - 19832 - mybro - loadUrl -  - Loading URL: https://www.amazon.com
2025-06-22T15:58:45.168302+1000 - __main__ - 113 - INFO - 19932 - 19832 - mybro - handleLoadFinished -  - Page loaded: https://www.amazon.com/
2025-06-22T15:59:06.117751+1000 - __main__ - 270 - DEBUG - 19932 - 19832 - mybro - goBack -  - Navigating back
2025-06-22T15:59:06.474057+1000 - __main__ - 113 - INFO - 19932 - 19832 - mybro - handleLoadFinished -  - Page loaded: https://www.google.com/
2025-06-22T15:59:07.526819+1000 - __main__ - 277 - DEBUG - 19932 - 19832 - mybro - goForward -  - Navigating forward
2025-06-22T15:59:07.669493+1000 - __main__ - 113 - INFO - 19932 - 19832 - mybro - handleLoadFinished -  - Page loaded: https://www.amazon.com/
2025-06-22T15:59:08.163059+1000 - __main__ - 314 - DEBUG - 19932 - 19832 - mybro - reloadPage -  - Reloading page
2025-06-22T15:59:10.909357+1000 - __main__ - 113 - INFO - 19932 - 19832 - mybro - handleLoadFinished -  - Page loaded: https://www.amazon.com.au/?ref_=mr_direct_us_au_au&showmri
2025-06-22T16:10:36.195618+1000 - __main__ - 490 - DEBUG - 20220 - 1620 - mybro - tabChanged -  - Switched to tab 0: https://www.google.com
2025-06-22T16:10:36.197613+1000 - __main__ - 237 - INFO - 20220 - 1620 - mybro - addBrowserTab -  - New tab created (index: 0)
2025-06-22T16:10:36.198611+1000 - __main__ - 223 - INFO - 20220 - 1620 - mybro - __init__ -  - Browser initialized
2025-06-22T16:10:37.411155+1000 - __main__ - 113 - INFO - 20220 - 1620 - mybro - handleLoadFinished -  - Page loaded: https://www.google.com/
2025-06-22T16:29:52.743410+1000 - __main__ - 535 - DEBUG - 7264 - 16056 - mybro - updateSourceButton -  - Source button disabled
2025-06-22T16:29:52.744408+1000 - __main__ - 524 - DEBUG - 7264 - 16056 - mybro - tabChanged -  - Switched to tab 0: https://www.google.com
2025-06-22T16:29:52.744408+1000 - __main__ - 433 - INFO - 7264 - 16056 - mybro - addBrowserTab -  - New tab created (index: 0)
2025-06-22T16:29:52.745405+1000 - __main__ - 514 - INFO - 7264 - 16056 - mybro - setupShortcuts -  - Keyboard shortcuts initialized
2025-06-22T16:29:52.745405+1000 - __main__ - 301 - INFO - 7264 - 16056 - mybro - __init__ -  - Browser initialized
2025-06-22T16:29:54.003828+1000 - __main__ - 535 - DEBUG - 7264 - 16056 - mybro - updateSourceButton -  - Source button enabled
2025-06-22T16:29:54.003828+1000 - __main__ - 190 - INFO - 7264 - 16056 - mybro - handleLoadFinished -  - Page loaded: https://www.google.com/
2025-06-22T16:30:02.877918+1000 - __main__ - 495 - INFO - 7264 - 16056 - mybro - loadUrl -  - Loading URL: https://www.amazon.com
2025-06-22T16:30:03.561403+1000 - __main__ - 535 - DEBUG - 7264 - 16056 - mybro - updateSourceButton -  - Source button enabled
2025-06-22T16:30:03.561403+1000 - __main__ - 190 - INFO - 7264 - 16056 - mybro - handleLoadFinished -  - Page loaded: https://www.amazon.com/
2025-06-22T16:30:09.901046+1000 - __main__ - 158 - INFO - 7264 - 16056 - mybro - _handle_source_result -  - Page source saved to: d:\Code\Python\alan\2nd\temp\www.amazon.com_20250622_163009.html
2025-06-22T16:34:54.120671+1000 - __main__ - 536 - DEBUG - 8504 - 8168 - mybro - updateSourceButton -  - Source button disabled
2025-06-22T16:34:54.121669+1000 - __main__ - 525 - DEBUG - 8504 - 8168 - mybro - tabChanged -  - Switched to tab 0: https://www.google.com
2025-06-22T16:34:54.121669+1000 - __main__ - 434 - INFO - 8504 - 8168 - mybro - addBrowserTab -  - New tab created (index: 0)
2025-06-22T16:34:54.122665+1000 - __main__ - 515 - INFO - 8504 - 8168 - mybro - setupShortcuts -  - Keyboard shortcuts initialized
2025-06-22T16:34:54.122665+1000 - __main__ - 301 - INFO - 8504 - 8168 - mybro - __init__ -  - Browser initialized
2025-06-22T16:34:55.776035+1000 - __main__ - 536 - DEBUG - 8504 - 8168 - mybro - updateSourceButton -  - Source button enabled
2025-06-22T16:34:55.776035+1000 - __main__ - 190 - INFO - 8504 - 8168 - mybro - handleLoadFinished -  - Page loaded: https://www.google.com/
2025-06-22T16:35:05.124885+1000 - __main__ - 496 - INFO - 8504 - 8168 - mybro - loadUrl -  - Loading URL: https://www.youtube.com
2025-06-22T16:35:07.802822+1000 - __main__ - 536 - DEBUG - 8504 - 8168 - mybro - updateSourceButton -  - Source button enabled
2025-06-22T16:35:07.802822+1000 - __main__ - 190 - INFO - 8504 - 8168 - mybro - handleLoadFinished -  - Page loaded: https://www.youtube.com/
2025-06-22T16:35:13.623291+1000 - __main__ - 158 - INFO - 8504 - 8168 - mybro - _handle_source_result -  - Page source saved to: d:\Code\Python\alan\2nd\temp\www.youtube.com_20250622_163513.html
2025-06-22T18:15:53.792612+1000 - __main__ - 496 - INFO - 8504 - 8168 - mybro - loadUrl -  - Loading URL: https://www.taobao.com
2025-06-22T18:15:58.272685+1000 - __main__ - 536 - DEBUG - 8504 - 8168 - mybro - updateSourceButton -  - Source button enabled
2025-06-22T18:15:58.272685+1000 - __main__ - 190 - INFO - 8504 - 8168 - mybro - handleLoadFinished -  - Page loaded: https://www.taobao.com/
2025-06-22T18:18:47.841824+1000 - __main__ - 554 - DEBUG - 10612 - 7972 - mybro - updateSourceButton -  - Source button hidden
2025-06-22T18:18:47.842822+1000 - __main__ - 524 - DEBUG - 10612 - 7972 - mybro - tabChanged -  - Switched to tab 0: https://www.google.com
2025-06-22T18:18:47.842822+1000 - __main__ - 433 - INFO - 10612 - 7972 - mybro - addBrowserTab -  - New tab created (index: 0)
2025-06-22T18:18:47.843819+1000 - __main__ - 514 - INFO - 10612 - 7972 - mybro - setupShortcuts -  - Keyboard shortcuts initialized
2025-06-22T18:18:47.843819+1000 - __main__ - 304 - INFO - 10612 - 7972 - mybro - __init__ -  - Browser initialized
2025-06-22T18:18:48.401646+1000 - __main__ - 554 - DEBUG - 10612 - 7972 - mybro - updateSourceButton -  - Source button hidden
2025-06-22T18:18:49.038956+1000 - __main__ - 550 - DEBUG - 10612 - 7972 - mybro - updateSourceButton -  - Source button shown and enabled
2025-06-22T18:18:49.038956+1000 - __main__ - 190 - INFO - 10612 - 7972 - mybro - handleLoadFinished -  - Page loaded: https://www.google.com/
2025-06-22T18:18:57.393587+1000 - __main__ - 495 - INFO - 10612 - 7972 - mybro - loadUrl -  - Loading URL: https://www.taobao.com
2025-06-22T18:18:57.517005+1000 - __main__ - 554 - DEBUG - 10612 - 7972 - mybro - updateSourceButton -  - Source button hidden
2025-06-22T18:19:02.293220+1000 - __main__ - 550 - DEBUG - 10612 - 7972 - mybro - updateSourceButton -  - Source button shown and enabled
2025-06-22T18:19:02.293220+1000 - __main__ - 190 - INFO - 10612 - 7972 - mybro - handleLoadFinished -  - Page loaded: https://www.taobao.com/
2025-06-22T18:22:59.587384+1000 - __main__ - 616 - DEBUG - 23268 - 20412 - mybro - updateSourceButton -  - Source button hidden
2025-06-22T18:22:59.587384+1000 - __main__ - 586 - DEBUG - 23268 - 20412 - mybro - tabChanged -  - Switched to tab 0: https://www.google.com
2025-06-22T18:22:59.588382+1000 - __main__ - 495 - INFO - 23268 - 20412 - mybro - addBrowserTab -  - New tab created (index: 0)
2025-06-22T18:22:59.588382+1000 - __main__ - 576 - INFO - 23268 - 20412 - mybro - setupShortcuts -  - Keyboard shortcuts initialized
2025-06-22T18:22:59.588382+1000 - __main__ - 366 - INFO - 23268 - 20412 - mybro - __init__ -  - Browser initialized
2025-06-22T18:23:00.127113+1000 - __main__ - 616 - DEBUG - 23268 - 20412 - mybro - updateSourceButton -  - Source button hidden
2025-06-22T18:23:00.697355+1000 - __main__ - 612 - DEBUG - 23268 - 20412 - mybro - updateSourceButton -  - Source button shown and enabled
2025-06-22T18:23:00.697355+1000 - __main__ - 252 - INFO - 23268 - 20412 - mybro - handleLoadFinished -  - Page loaded: https://www.google.com/
2025-06-22T18:23:05.852453+1000 - __main__ - 144 - INFO - 23268 - 20412 - mybro - _handle_source_result -  - Page source retrieved, length: 193052 characters
2025-06-22T18:26:48.067885+1000 - __main__ - 700 - DEBUG - 6324 - 13940 - mybro - updateSourceButton -  - Source button hidden
2025-06-22T18:26:48.067885+1000 - __main__ - 670 - DEBUG - 6324 - 13940 - mybro - tabChanged -  - Switched to tab 0: https://www.google.com
2025-06-22T18:26:48.067885+1000 - __main__ - 579 - INFO - 6324 - 13940 - mybro - addBrowserTab -  - New tab created (index: 0)
2025-06-22T18:26:48.068883+1000 - __main__ - 660 - INFO - 6324 - 13940 - mybro - setupShortcuts -  - Keyboard shortcuts initialized
2025-06-22T18:26:48.068883+1000 - __main__ - 450 - INFO - 6324 - 13940 - mybro - __init__ -  - Browser initialized
2025-06-22T18:26:48.630209+1000 - __main__ - 700 - DEBUG - 6324 - 13940 - mybro - updateSourceButton -  - Source button hidden
2025-06-22T18:26:49.222575+1000 - __main__ - 696 - DEBUG - 6324 - 13940 - mybro - updateSourceButton -  - Source button shown and enabled
2025-06-22T18:26:49.222575+1000 - __main__ - 336 - INFO - 6324 - 13940 - mybro - handleLoadFinished -  - Page loaded: https://www.google.com/
2025-06-22T18:26:50.868011+1000 - __main__ - 144 - INFO - 6324 - 13940 - mybro - _handle_source_result -  - Page source retrieved, length: 192567 characters
2025-06-22T18:27:19.907583+1000 - __main__ - 700 - DEBUG - 2456 - 23360 - mybro - updateSourceButton -  - Source button hidden
2025-06-22T18:27:19.907583+1000 - __main__ - 670 - DEBUG - 2456 - 23360 - mybro - tabChanged -  - Switched to tab 0: https://www.google.com
2025-06-22T18:27:19.907583+1000 - __main__ - 579 - INFO - 2456 - 23360 - mybro - addBrowserTab -  - New tab created (index: 0)
2025-06-22T18:27:19.908581+1000 - __main__ - 660 - INFO - 2456 - 23360 - mybro - setupShortcuts -  - Keyboard shortcuts initialized
2025-06-22T18:27:19.908581+1000 - __main__ - 450 - INFO - 2456 - 23360 - mybro - __init__ -  - Browser initialized
2025-06-22T18:27:20.373230+1000 - __main__ - 700 - DEBUG - 2456 - 23360 - mybro - updateSourceButton -  - Source button hidden
2025-06-22T18:27:21.242868+1000 - __main__ - 696 - DEBUG - 2456 - 23360 - mybro - updateSourceButton -  - Source button shown and enabled
2025-06-22T18:27:21.242868+1000 - __main__ - 336 - INFO - 2456 - 23360 - mybro - handleLoadFinished -  - Page loaded: https://www.google.com/
2025-06-22T18:27:23.768655+1000 - __main__ - 144 - INFO - 2456 - 23360 - mybro - _handle_source_result -  - Page source retrieved, length: 192213 characters
2025-06-22T18:28:06.730154+1000 - __main__ - 144 - INFO - 2456 - 23360 - mybro - _handle_source_result -  - Page source retrieved, length: 192213 characters
2025-06-22T18:32:32.904053+1000 - __main__ - 702 - DEBUG - 15964 - 6460 - mybro - updateSourceButton -  - Source button hidden
2025-06-22T18:32:32.905050+1000 - __main__ - 672 - DEBUG - 15964 - 6460 - mybro - tabChanged -  - Switched to tab 0: https://www.google.com
2025-06-22T18:32:32.905050+1000 - __main__ - 581 - INFO - 15964 - 6460 - mybro - addBrowserTab -  - New tab created (index: 0)
2025-06-22T18:32:32.905050+1000 - __main__ - 662 - INFO - 15964 - 6460 - mybro - setupShortcuts -  - Keyboard shortcuts initialized
2025-06-22T18:32:32.906048+1000 - __main__ - 452 - INFO - 15964 - 6460 - mybro - __init__ -  - Browser initialized
2025-06-22T18:32:33.390833+1000 - __main__ - 702 - DEBUG - 15964 - 6460 - mybro - updateSourceButton -  - Source button hidden
2025-06-22T18:32:34.137563+1000 - __main__ - 698 - DEBUG - 15964 - 6460 - mybro - updateSourceButton -  - Source button shown and enabled
2025-06-22T18:32:34.137563+1000 - __main__ - 338 - INFO - 15964 - 6460 - mybro - handleLoadFinished -  - Page loaded: https://www.google.com/
2025-06-22T18:32:35.836349+1000 - __main__ - 144 - INFO - 15964 - 6460 - mybro - _handle_source_result -  - Page source retrieved, length: 192242 characters
2025-06-22T18:32:38.296256+1000 - __main__ - 243 - INFO - 15964 - 6460 - mybro - format_html_source -  - HTML source formatted and highlighted
2025-06-22T18:32:44.428624+1000 - __main__ - 243 - INFO - 15964 - 6460 - mybro - format_html_source -  - HTML source formatted and highlighted
2025-06-22T18:54:45.940800+1000 - __main__ - 773 - DEBUG - 2724 - 21680 - mybro - updateSourceButton -  - Source button hidden
2025-06-22T18:54:45.941798+1000 - __main__ - 743 - DEBUG - 2724 - 21680 - mybro - tabChanged -  - Switched to tab 0: https://www.google.com
2025-06-22T18:54:45.941798+1000 - __main__ - 652 - INFO - 2724 - 21680 - mybro - addBrowserTab -  - New tab created (index: 0)
2025-06-22T18:54:45.942795+1000 - __main__ - 733 - INFO - 2724 - 21680 - mybro - setupShortcuts -  - Keyboard shortcuts initialized
2025-06-22T18:54:45.942795+1000 - __main__ - 523 - INFO - 2724 - 21680 - mybro - __init__ -  - Browser initialized
2025-06-22T18:54:46.516558+1000 - __main__ - 773 - DEBUG - 2724 - 21680 - mybro - updateSourceButton -  - Source button hidden
2025-06-22T18:54:47.053610+1000 - __main__ - 769 - DEBUG - 2724 - 21680 - mybro - updateSourceButton -  - Source button shown and enabled
2025-06-22T18:54:47.053610+1000 - __main__ - 409 - INFO - 2724 - 21680 - mybro - handleLoadFinished -  - Page loaded: https://www.google.com/
2025-06-22T18:54:49.193366+1000 - __main__ - 147 - INFO - 2724 - 21680 - mybro - _handle_source_result -  - Page source retrieved, length: 191955 characters
2025-06-22T18:54:51.212424+1000 - __main__ - 318 - WARNING - 2724 - 21680 - mybro - _apply_html_formatting -  - BeautifulSoup not installed. Using basic HTML formatting.
2025-06-22T18:54:51.358802+1000 - __main__ - 250 - INFO - 2724 - 21680 - mybro - format_html_source -  - HTML source formatted and highlighted
2025-06-22T18:55:57.262706+1000 - __main__ - 773 - DEBUG - 18132 - 19444 - mybro - updateSourceButton -  - Source button hidden
2025-06-22T18:55:57.263704+1000 - __main__ - 743 - DEBUG - 18132 - 19444 - mybro - tabChanged -  - Switched to tab 0: https://www.google.com
2025-06-22T18:55:57.263704+1000 - __main__ - 652 - INFO - 18132 - 19444 - mybro - addBrowserTab -  - New tab created (index: 0)
2025-06-22T18:55:57.264702+1000 - __main__ - 733 - INFO - 18132 - 19444 - mybro - setupShortcuts -  - Keyboard shortcuts initialized
2025-06-22T18:55:57.264702+1000 - __main__ - 523 - INFO - 18132 - 19444 - mybro - __init__ -  - Browser initialized
2025-06-22T18:55:57.792091+1000 - __main__ - 773 - DEBUG - 18132 - 19444 - mybro - updateSourceButton -  - Source button hidden
2025-06-22T18:55:58.633671+1000 - __main__ - 769 - DEBUG - 18132 - 19444 - mybro - updateSourceButton -  - Source button shown and enabled
2025-06-22T18:55:58.634669+1000 - __main__ - 409 - INFO - 18132 - 19444 - mybro - handleLoadFinished -  - Page loaded: https://www.google.com/
2025-06-22T18:56:00.432084+1000 - __main__ - 147 - INFO - 18132 - 19444 - mybro - _handle_source_result -  - Page source retrieved, length: 188898 characters
2025-06-22T18:56:02.112087+1000 - __main__ - 318 - WARNING - 18132 - 19444 - mybro - _apply_html_formatting -  - BeautifulSoup not installed. Using basic HTML formatting.
2025-06-22T18:56:02.255703+1000 - __main__ - 250 - INFO - 18132 - 19444 - mybro - format_html_source -  - HTML source formatted and highlighted
2025-06-22T19:06:40.830017+1000 - __main__ - 773 - DEBUG - 17308 - 19344 - mybro - updateSourceButton -  - Source button hidden
2025-06-22T19:06:40.830017+1000 - __main__ - 743 - DEBUG - 17308 - 19344 - mybro - tabChanged -  - Switched to tab 0: https://www.google.com
2025-06-22T19:06:40.831015+1000 - __main__ - 652 - INFO - 17308 - 19344 - mybro - addBrowserTab -  - New tab created (index: 0)
2025-06-22T19:06:40.832012+1000 - __main__ - 733 - INFO - 17308 - 19344 - mybro - setupShortcuts -  - Keyboard shortcuts initialized
2025-06-22T19:06:40.832012+1000 - __main__ - 523 - INFO - 17308 - 19344 - mybro - __init__ -  - Browser initialized
2025-06-22T19:06:41.335823+1000 - __main__ - 773 - DEBUG - 17308 - 19344 - mybro - updateSourceButton -  - Source button hidden
2025-06-22T19:06:41.940598+1000 - __main__ - 769 - DEBUG - 17308 - 19344 - mybro - updateSourceButton -  - Source button shown and enabled
2025-06-22T19:06:41.940598+1000 - __main__ - 409 - INFO - 17308 - 19344 - mybro - handleLoadFinished -  - Page loaded: https://www.google.com/
2025-06-22T19:06:43.049191+1000 - __main__ - 147 - INFO - 17308 - 19344 - mybro - _handle_source_result -  - Page source retrieved, length: 191873 characters
2025-06-22T19:06:45.637762+1000 - __main__ - 318 - WARNING - 17308 - 19344 - mybro - _apply_html_formatting -  - BeautifulSoup not installed. Using basic HTML formatting.
2025-06-22T19:06:45.778205+1000 - __main__ - 250 - INFO - 17308 - 19344 - mybro - format_html_source -  - HTML source formatted and highlighted
2025-06-22T19:08:21.374165+1000 - __main__ - 773 - DEBUG - 16548 - 12068 - mybro - updateSourceButton -  - Source button hidden
2025-06-22T19:08:21.374165+1000 - __main__ - 743 - DEBUG - 16548 - 12068 - mybro - tabChanged -  - Switched to tab 0: https://www.google.com
2025-06-22T19:08:21.375163+1000 - __main__ - 652 - INFO - 16548 - 12068 - mybro - addBrowserTab -  - New tab created (index: 0)
2025-06-22T19:08:21.375163+1000 - __main__ - 733 - INFO - 16548 - 12068 - mybro - setupShortcuts -  - Keyboard shortcuts initialized
2025-06-22T19:08:21.375163+1000 - __main__ - 523 - INFO - 16548 - 12068 - mybro - __init__ -  - Browser initialized
2025-06-22T19:08:21.903592+1000 - __main__ - 773 - DEBUG - 16548 - 12068 - mybro - updateSourceButton -  - Source button hidden
2025-06-22T19:08:22.642922+1000 - __main__ - 769 - DEBUG - 16548 - 12068 - mybro - updateSourceButton -  - Source button shown and enabled
2025-06-22T19:08:22.643919+1000 - __main__ - 409 - INFO - 16548 - 12068 - mybro - handleLoadFinished -  - Page loaded: https://www.google.com/
2025-06-22T19:08:23.816128+1000 - __main__ - 147 - INFO - 16548 - 12068 - mybro - _handle_source_result -  - Page source retrieved, length: 192256 characters
2025-06-22T19:08:26.351787+1000 - __main__ - 318 - WARNING - 16548 - 12068 - mybro - _apply_html_formatting -  - BeautifulSoup not installed. Using basic HTML formatting.
2025-06-22T19:08:26.491138+1000 - __main__ - 250 - INFO - 16548 - 12068 - mybro - format_html_source -  - HTML source formatted and highlighted
2025-06-22T19:08:28.613621+1000 - __main__ - 382 - INFO - 16548 - 12068 - mybro - save_source_to_file -  - Page source saved to: d:\Code\Python\alan\2nd\temp\www.google.com_20250622_190828.html
2025-06-22T19:14:51.176344+1000 - __main__ - 832 - DEBUG - 19768 - 23188 - mybro - updateSourceButton -  - Source button hidden
2025-06-22T19:14:51.177341+1000 - __main__ - 802 - DEBUG - 19768 - 23188 - mybro - tabChanged -  - Switched to tab 0: https://www.google.com
2025-06-22T19:14:51.177341+1000 - __main__ - 711 - INFO - 19768 - 23188 - mybro - addBrowserTab -  - New tab created (index: 0)
2025-06-22T19:14:51.178339+1000 - __main__ - 792 - INFO - 19768 - 23188 - mybro - setupShortcuts -  - Keyboard shortcuts initialized
2025-06-22T19:14:51.178339+1000 - __main__ - 582 - INFO - 19768 - 23188 - mybro - __init__ -  - Browser initialized
2025-06-22T19:14:51.849418+1000 - __main__ - 832 - DEBUG - 19768 - 23188 - mybro - updateSourceButton -  - Source button hidden
2025-06-22T19:14:52.511974+1000 - __main__ - 828 - DEBUG - 19768 - 23188 - mybro - updateSourceButton -  - Source button shown and enabled
2025-06-22T19:14:52.511974+1000 - __main__ - 468 - INFO - 19768 - 23188 - mybro - handleLoadFinished -  - Page loaded: https://www.google.com/
2025-06-22T19:14:53.677082+1000 - __main__ - 157 - INFO - 19768 - 23188 - mybro - _handle_source_result -  - Page source retrieved, length: 192320 characters
2025-06-22T19:14:55.506727+1000 - __main__ - 377 - WARNING - 19768 - 23188 - mybro - _apply_html_formatting -  - BeautifulSoup not installed. Using basic HTML formatting.
2025-06-22T19:14:55.655440+1000 - __main__ - 309 - INFO - 19768 - 23188 - mybro - format_html_source -  - HTML source formatted and highlighted
2025-06-22T19:18:04.349224+1000 - __main__ - 931 - DEBUG - 20848 - 21232 - mybro - updateSourceButton -  - Source button hidden
2025-06-22T19:18:04.350222+1000 - __main__ - 901 - DEBUG - 20848 - 21232 - mybro - tabChanged -  - Switched to tab 0: https://www.google.com
2025-06-22T19:18:04.350222+1000 - __main__ - 810 - INFO - 20848 - 21232 - mybro - addBrowserTab -  - New tab created (index: 0)
2025-06-22T19:18:04.350222+1000 - __main__ - 891 - INFO - 20848 - 21232 - mybro - setupShortcuts -  - Keyboard shortcuts initialized
2025-06-22T19:18:04.351219+1000 - __main__ - 681 - INFO - 20848 - 21232 - mybro - __init__ -  - Browser initialized
2025-06-22T19:18:04.828825+1000 - __main__ - 931 - DEBUG - 20848 - 21232 - mybro - updateSourceButton -  - Source button hidden
2025-06-22T19:18:05.876124+1000 - __main__ - 927 - DEBUG - 20848 - 21232 - mybro - updateSourceButton -  - Source button shown and enabled
2025-06-22T19:18:05.876124+1000 - __main__ - 567 - INFO - 20848 - 21232 - mybro - handleLoadFinished -  - Page loaded: https://www.google.com/
2025-06-22T19:18:06.857037+1000 - __main__ - 159 - INFO - 20848 - 21232 - mybro - _handle_source_result -  - Page source retrieved, length: 189538 characters
2025-06-22T19:18:09.140945+1000 - __main__ - 338 - WARNING - 20848 - 21232 - mybro - format_html_source -  - jsbeautifier not available. Trying alternative method.
2025-06-22T19:18:09.141942+1000 - __main__ - 366 - WARNING - 20848 - 21232 - mybro - format_html_source -  - html-tidy not available. Trying alternative method.
2025-06-22T19:18:09.144934+1000 - __main__ - 442 - WARNING - 20848 - 21232 - mybro - _apply_html_formatting -  - BeautifulSoup not installed. Using basic HTML formatting.
2025-06-22T19:18:09.285809+1000 - __main__ - 374 - INFO - 20848 - 21232 - mybro - format_html_source -  - HTML source formatted and highlighted
2025-06-22T19:18:10.250931+1000 - __main__ - 338 - WARNING - 20848 - 21232 - mybro - format_html_source -  - jsbeautifier not available. Trying alternative method.
2025-06-22T19:18:10.250931+1000 - __main__ - 366 - WARNING - 20848 - 21232 - mybro - format_html_source -  - html-tidy not available. Trying alternative method.
2025-06-22T19:18:10.251928+1000 - __main__ - 442 - WARNING - 20848 - 21232 - mybro - _apply_html_formatting -  - BeautifulSoup not installed. Using basic HTML formatting.
2025-06-22T19:18:10.391947+1000 - __main__ - 374 - INFO - 20848 - 21232 - mybro - format_html_source -  - HTML source formatted and highlighted
2025-06-22T19:18:12.059547+1000 - __main__ - 551 - INFO - 20848 - 21232 - mybro - open_in_external_editor -  - Source code opened in external editor: d:\Code\Python\alan\2nd\temp\www.google.com_20250622_191811.html
2025-06-22T19:19:46.963720+1000 - __main__ - 968 - DEBUG - 22176 - 23340 - mybro - updateSourceButton -  - Source button hidden
2025-06-22T19:19:46.963720+1000 - __main__ - 938 - DEBUG - 22176 - 23340 - mybro - tabChanged -  - Switched to tab 0: https://www.google.com
2025-06-22T19:19:46.964718+1000 - __main__ - 847 - INFO - 22176 - 23340 - mybro - addBrowserTab -  - New tab created (index: 0)
2025-06-22T19:19:46.964718+1000 - __main__ - 928 - INFO - 22176 - 23340 - mybro - setupShortcuts -  - Keyboard shortcuts initialized
2025-06-22T19:19:46.964718+1000 - __main__ - 718 - INFO - 22176 - 23340 - mybro - __init__ -  - Browser initialized
2025-06-22T19:19:47.511029+1000 - __main__ - 968 - DEBUG - 22176 - 23340 - mybro - updateSourceButton -  - Source button hidden
2025-06-22T19:19:48.065781+1000 - __main__ - 964 - DEBUG - 22176 - 23340 - mybro - updateSourceButton -  - Source button shown and enabled
2025-06-22T19:19:48.066779+1000 - __main__ - 604 - INFO - 22176 - 23340 - mybro - handleLoadFinished -  - Page loaded: https://www.google.com/
2025-06-22T19:19:49.678825+1000 - __main__ - 159 - INFO - 22176 - 23340 - mybro - _handle_source_result -  - Page source retrieved, length: 192193 characters
2025-06-22T19:19:52.105308+1000 - __main__ - 338 - WARNING - 22176 - 23340 - mybro - format_html_source -  - jsbeautifier not available. Trying alternative method.
2025-06-22T19:19:52.106306+1000 - __main__ - 366 - WARNING - 22176 - 23340 - mybro - format_html_source -  - html-tidy not available. Trying alternative method.
2025-06-22T19:19:52.110295+1000 - __main__ - 442 - WARNING - 22176 - 23340 - mybro - _apply_html_formatting -  - BeautifulSoup not installed. Using basic HTML formatting.
2025-06-22T19:19:52.250876+1000 - __main__ - 374 - INFO - 22176 - 23340 - mybro - format_html_source -  - HTML source formatted and highlighted
2025-06-22T19:19:54.594212+1000 - __main__ - 590 - ERROR - 22176 - 23340 - mybro - open_in_external_editor -  - Error opening in external editor: QDesktopServices.openUrl returned False
2025-06-22T19:23:57.370494+1000 - __main__ - 954 - DEBUG - 14592 - 6240 - mybro - updateSourceButton -  - Source button hidden
2025-06-22T19:23:57.371491+1000 - __main__ - 924 - DEBUG - 14592 - 6240 - mybro - tabChanged -  - Switched to tab 0: https://www.google.com
2025-06-22T19:23:57.371491+1000 - __main__ - 833 - INFO - 14592 - 6240 - mybro - addBrowserTab -  - New tab created (index: 0)
2025-06-22T19:23:57.372489+1000 - __main__ - 914 - INFO - 14592 - 6240 - mybro - setupShortcuts -  - Keyboard shortcuts initialized
2025-06-22T19:23:57.372489+1000 - __main__ - 704 - INFO - 14592 - 6240 - mybro - __init__ -  - Browser initialized
2025-06-22T19:23:57.874803+1000 - __main__ - 954 - DEBUG - 14592 - 6240 - mybro - updateSourceButton -  - Source button hidden
2025-06-22T19:23:58.571081+1000 - __main__ - 950 - DEBUG - 14592 - 6240 - mybro - updateSourceButton -  - Source button shown and enabled
2025-06-22T19:23:58.571081+1000 - __main__ - 590 - INFO - 14592 - 6240 - mybro - handleLoadFinished -  - Page loaded: https://www.google.com/
2025-06-22T19:23:59.837546+1000 - __main__ - 159 - INFO - 14592 - 6240 - mybro - _handle_source_result -  - Page source retrieved, length: 192288 characters
2025-06-22T19:24:14.206366+1000 - __main__ - 568 - INFO - 14592 - 6240 - mybro - open_in_external_editor -  - Source code opened in external editor: d:\Code\Python\alan\2nd\temp\www.google.com_20250622_192402.html
2025-06-22T19:50:19.963622+1000 - __main__ - 963 - DEBUG - 22824 - 540 - mybro - updateSourceButton -  - Source button hidden
2025-06-22T19:50:19.964621+1000 - __main__ - 949 - DEBUG - 22824 - 540 - mybro - tabChanged -  - Switched to tab 0: https://www.google.com
2025-06-22T19:50:19.965618+1000 - __main__ - 865 - INFO - 22824 - 540 - mybro - addBrowserTab -  - New tab created (index: 0)
2025-06-22T19:50:19.965618+1000 - __main__ - 939 - INFO - 22824 - 540 - mybro - setupShortcuts -  - Keyboard shortcuts initialized
2025-06-22T19:50:19.965618+1000 - __main__ - 791 - INFO - 22824 - 540 - mybro - __init__ -  - Browser initialized
2025-06-22T19:50:20.492787+1000 - __main__ - 963 - DEBUG - 22824 - 540 - mybro - updateSourceButton -  - Source button hidden
2025-06-22T19:50:21.151239+1000 - __main__ - 960 - DEBUG - 22824 - 540 - mybro - updateSourceButton -  - Source button shown and enabled
2025-06-22T19:50:21.151239+1000 - __main__ - 677 - INFO - 22824 - 540 - mybro - handleLoadFinished -  - Page loaded: https://www.google.com/
2025-06-22T19:50:25.920636+1000 - __main__ - 159 - INFO - 22824 - 540 - mybro - _handle_source_result -  - Page source retrieved, length: 191850 characters
2025-06-22T19:50:28.867770+1000 - __main__ - 338 - WARNING - 22824 - 540 - mybro - format_html_source -  - jsbeautifier not available. Trying alternative method.
2025-06-22T19:50:28.868768+1000 - __main__ - 366 - WARNING - 22824 - 540 - mybro - format_html_source -  - html-tidy not available. Trying alternative method.
2025-06-22T19:50:28.871759+1000 - __main__ - 442 - WARNING - 22824 - 540 - mybro - _apply_html_formatting -  - BeautifulSoup not installed. Using basic HTML formatting.
2025-06-22T19:50:29.033647+1000 - __main__ - 374 - INFO - 22824 - 540 - mybro - format_html_source -  - HTML source formatted and highlighted
2025-06-22T19:50:32.805218+1000 - __main__ - 552 - INFO - 22824 - 540 - mybro - open_in_external_editor -  - Opened source code with QDesktopServices: d:\Code\Python\alan\2nd\temp\www.google.com_20250622_195032.html
2025-06-22T19:54:57.975757+1000 - __main__ - 931 - DEBUG - 11564 - 21808 - mybro - updateSourceButton -  - Source button hidden
2025-06-22T19:54:57.975757+1000 - __main__ - 917 - DEBUG - 11564 - 21808 - mybro - tabChanged -  - Switched to tab 0: https://www.google.com
2025-06-22T19:54:57.975757+1000 - __main__ - 833 - INFO - 11564 - 21808 - mybro - addBrowserTab -  - New tab created (index: 0)
2025-06-22T19:54:57.976755+1000 - __main__ - 907 - INFO - 11564 - 21808 - mybro - setupShortcuts -  - Keyboard shortcuts initialized
2025-06-22T19:54:57.976755+1000 - __main__ - 759 - INFO - 11564 - 21808 - mybro - __init__ -  - Browser initialized
2025-06-22T19:54:58.671512+1000 - __main__ - 931 - DEBUG - 11564 - 21808 - mybro - updateSourceButton -  - Source button hidden
2025-06-22T19:54:59.260273+1000 - __main__ - 928 - DEBUG - 11564 - 21808 - mybro - updateSourceButton -  - Source button shown and enabled
2025-06-22T19:54:59.260273+1000 - __main__ - 645 - INFO - 11564 - 21808 - mybro - handleLoadFinished -  - Page loaded: https://www.google.com/
2025-06-22T19:55:00.908196+1000 - __main__ - 159 - INFO - 11564 - 21808 - mybro - _handle_source_result -  - Page source retrieved, length: 189218 characters
2025-06-22T19:55:03.111596+1000 - __main__ - 338 - WARNING - 11564 - 21808 - mybro - format_html_source -  - jsbeautifier not available. Trying alternative method.
2025-06-22T19:55:03.112593+1000 - __main__ - 366 - WARNING - 11564 - 21808 - mybro - format_html_source -  - html-tidy not available. Trying alternative method.
2025-06-22T19:55:03.194889+1000 - __main__ - 372 - INFO - 11564 - 21808 - mybro - format_html_source -  - HTML source formatted with basic formatter
2025-06-22T19:58:29.916954+1000 - __main__ - 912 - DEBUG - 10536 - 5148 - mybro - updateSourceButton -  - Source button hidden
2025-06-22T19:58:29.916954+1000 - __main__ - 898 - DEBUG - 10536 - 5148 - mybro - tabChanged -  - Switched to tab 0: https://www.google.com
2025-06-22T19:58:29.917952+1000 - __main__ - 814 - INFO - 10536 - 5148 - mybro - addBrowserTab -  - New tab created (index: 0)
2025-06-22T19:58:29.917952+1000 - __main__ - 888 - INFO - 10536 - 5148 - mybro - setupShortcuts -  - Keyboard shortcuts initialized
2025-06-22T19:58:29.917952+1000 - __main__ - 740 - INFO - 10536 - 5148 - mybro - __init__ -  - Browser initialized
2025-06-22T19:58:30.487816+1000 - __main__ - 912 - DEBUG - 10536 - 5148 - mybro - updateSourceButton -  - Source button hidden
2025-06-22T19:58:31.058378+1000 - __main__ - 909 - DEBUG - 10536 - 5148 - mybro - updateSourceButton -  - Source button shown and enabled
2025-06-22T19:58:31.058378+1000 - __main__ - 626 - INFO - 10536 - 5148 - mybro - handleLoadFinished -  - Page loaded: https://www.google.com/
2025-06-22T19:58:31.762776+1000 - __main__ - 159 - INFO - 10536 - 5148 - mybro - _handle_source_result -  - Page source retrieved, length: 192631 characters
2025-06-22T20:05:08.332578+1000 - __main__ - 905 - DEBUG - 22800 - 19092 - mybro - updateSourceButton -  - Source button hidden
2025-06-22T20:05:08.332578+1000 - __main__ - 891 - DEBUG - 22800 - 19092 - mybro - tabChanged -  - Switched to tab 0: https://www.google.com
2025-06-22T20:05:08.333575+1000 - __main__ - 807 - INFO - 22800 - 19092 - mybro - addBrowserTab -  - New tab created (index: 0)
2025-06-22T20:05:08.333575+1000 - __main__ - 881 - INFO - 22800 - 19092 - mybro - setupShortcuts -  - Keyboard shortcuts initialized
2025-06-22T20:05:08.333575+1000 - __main__ - 733 - INFO - 22800 - 19092 - mybro - __init__ -  - Browser initialized
2025-06-22T20:05:08.864708+1000 - __main__ - 905 - DEBUG - 22800 - 19092 - mybro - updateSourceButton -  - Source button hidden
2025-06-22T20:05:09.479320+1000 - __main__ - 902 - DEBUG - 22800 - 19092 - mybro - updateSourceButton -  - Source button shown and enabled
2025-06-22T20:05:09.479320+1000 - __main__ - 619 - INFO - 22800 - 19092 - mybro - handleLoadFinished -  - Page loaded: https://www.google.com/
2025-06-22T20:05:10.882895+1000 - __main__ - 159 - INFO - 22800 - 19092 - mybro - _handle_source_result -  - Page source retrieved, length: 192358 characters
2025-06-22T20:05:13.326263+1000 - __main__ - 357 - WARNING - 22800 - 19092 - mybro - format_html -  - BeautifulSoup not installed. Using basic HTML formatting.
2025-06-22T20:05:13.439352+1000 - __main__ - 362 - INFO - 22800 - 19092 - mybro - format_html -  - HTML source formatted and highlighted
2025-06-22T20:06:50.218592+1000 - __main__ - 905 - DEBUG - 22944 - 20396 - mybro - updateSourceButton -  - Source button hidden
2025-06-22T20:06:50.219590+1000 - __main__ - 891 - DEBUG - 22944 - 20396 - mybro - tabChanged -  - Switched to tab 0: https://www.google.com
2025-06-22T20:06:50.219590+1000 - __main__ - 807 - INFO - 22944 - 20396 - mybro - addBrowserTab -  - New tab created (index: 0)
2025-06-22T20:06:50.220587+1000 - __main__ - 881 - INFO - 22944 - 20396 - mybro - setupShortcuts -  - Keyboard shortcuts initialized
2025-06-22T20:06:50.220587+1000 - __main__ - 733 - INFO - 22944 - 20396 - mybro - __init__ -  - Browser initialized
2025-06-22T20:06:50.743512+1000 - __main__ - 905 - DEBUG - 22944 - 20396 - mybro - updateSourceButton -  - Source button hidden
2025-06-22T20:06:51.645630+1000 - __main__ - 902 - DEBUG - 22944 - 20396 - mybro - updateSourceButton -  - Source button shown and enabled
2025-06-22T20:06:51.645630+1000 - __main__ - 619 - INFO - 22944 - 20396 - mybro - handleLoadFinished -  - Page loaded: https://www.google.com/
2025-06-22T20:06:53.268866+1000 - __main__ - 159 - INFO - 22944 - 20396 - mybro - _handle_source_result -  - Page source retrieved, length: 189967 characters
2025-06-22T20:06:55.619811+1000 - __main__ - 357 - WARNING - 22944 - 20396 - mybro - format_html -  - BeautifulSoup not installed. Using basic HTML formatting.
2025-06-22T20:06:55.734953+1000 - __main__ - 362 - INFO - 22944 - 20396 - mybro - format_html -  - HTML source formatted and highlighted
2025-06-22T20:15:04.962722+1000 - __main__ - 985 - DEBUG - 15676 - 18324 - mybro - updateSourceButton -  - Source button hidden
2025-06-22T20:15:04.962722+1000 - __main__ - 971 - DEBUG - 15676 - 18324 - mybro - tabChanged -  - Switched to tab 0: https://www.google.com
2025-06-22T20:15:04.963720+1000 - __main__ - 887 - INFO - 15676 - 18324 - mybro - addBrowserTab -  - New tab created (index: 0)
2025-06-22T20:15:04.963720+1000 - __main__ - 961 - INFO - 15676 - 18324 - mybro - setupShortcuts -  - Keyboard shortcuts initialized
2025-06-22T20:15:04.963720+1000 - __main__ - 813 - INFO - 15676 - 18324 - mybro - __init__ -  - Browser initialized
2025-06-22T20:15:05.502074+1000 - __main__ - 985 - DEBUG - 15676 - 18324 - mybro - updateSourceButton -  - Source button hidden
2025-06-22T20:15:06.250303+1000 - __main__ - 982 - DEBUG - 15676 - 18324 - mybro - updateSourceButton -  - Source button shown and enabled
2025-06-22T20:15:06.251300+1000 - __main__ - 699 - INFO - 15676 - 18324 - mybro - handleLoadFinished -  - Page loaded: https://www.google.com/
2025-06-22T20:15:07.727291+1000 - __main__ - 159 - INFO - 15676 - 18324 - mybro - _handle_source_result -  - Page source retrieved, length: 192274 characters
2025-06-22T20:15:09.938662+1000 - __main__ - 369 - WARNING - 15676 - 18324 - mybro - format_html -  - BeautifulSoup not installed. Using basic HTML formatting.
2025-06-22T20:15:09.996402+1000 - __main__ - 374 - INFO - 15676 - 18324 - mybro - format_html -  - HTML source formatted and highlighted
2025-06-22T20:21:40.413628+1000 - __main__ - 1094 - DEBUG - 2248 - 21676 - mybro - updateSourceButton -  - Source button hidden
2025-06-22T20:21:40.413628+1000 - __main__ - 1080 - DEBUG - 2248 - 21676 - mybro - tabChanged -  - Switched to tab 0: https://www.google.com
2025-06-22T20:21:40.414626+1000 - __main__ - 996 - INFO - 2248 - 21676 - mybro - addBrowserTab -  - New tab created (index: 0)
2025-06-22T20:21:40.414626+1000 - __main__ - 1070 - INFO - 2248 - 21676 - mybro - setupShortcuts -  - Keyboard shortcuts initialized
2025-06-22T20:21:40.414626+1000 - __main__ - 922 - INFO - 2248 - 21676 - mybro - __init__ -  - Browser initialized
2025-06-22T20:21:41.481481+1000 - __main__ - 1094 - DEBUG - 2248 - 21676 - mybro - updateSourceButton -  - Source button hidden
2025-06-22T20:21:42.052514+1000 - __main__ - 1091 - DEBUG - 2248 - 21676 - mybro - updateSourceButton -  - Source button shown and enabled
2025-06-22T20:21:42.052514+1000 - __main__ - 808 - INFO - 2248 - 21676 - mybro - handleLoadFinished -  - Page loaded: https://www.google.com/
2025-06-22T20:21:47.964497+1000 - __main__ - 189 - INFO - 2248 - 21676 - mybro - _handle_source_result -  - Page source retrieved, length: 192565 characters
2025-06-22T20:21:47.964497+1000 - __main__ - 210 - WARNING - 2248 - 21676 - mybro - _handle_source_result -  - Minified or potentially obfuscated HTML detected
2025-06-22T20:22:02.766344+1000 - __main__ - 1094 - DEBUG - 15356 - 9208 - mybro - updateSourceButton -  - Source button hidden
2025-06-22T20:22:02.767342+1000 - __main__ - 1080 - DEBUG - 15356 - 9208 - mybro - tabChanged -  - Switched to tab 0: https://www.google.com
2025-06-22T20:22:02.767342+1000 - __main__ - 996 - INFO - 15356 - 9208 - mybro - addBrowserTab -  - New tab created (index: 0)
2025-06-22T20:22:02.768339+1000 - __main__ - 1070 - INFO - 15356 - 9208 - mybro - setupShortcuts -  - Keyboard shortcuts initialized
2025-06-22T20:22:02.768339+1000 - __main__ - 922 - INFO - 15356 - 9208 - mybro - __init__ -  - Browser initialized
2025-06-22T20:22:03.309523+1000 - __main__ - 1094 - DEBUG - 15356 - 9208 - mybro - updateSourceButton -  - Source button hidden
2025-06-22T20:22:03.859091+1000 - __main__ - 1091 - DEBUG - 15356 - 9208 - mybro - updateSourceButton -  - Source button shown and enabled
2025-06-22T20:22:03.859091+1000 - __main__ - 808 - INFO - 15356 - 9208 - mybro - handleLoadFinished -  - Page loaded: https://www.google.com/
2025-06-22T20:22:05.951486+1000 - __main__ - 189 - INFO - 15356 - 9208 - mybro - _handle_source_result -  - Page source retrieved, length: 192295 characters
2025-06-22T20:22:05.951486+1000 - __main__ - 210 - WARNING - 15356 - 9208 - mybro - _handle_source_result -  - Minified or potentially obfuscated HTML detected
2025-06-22T20:22:09.707606+1000 - __main__ - 437 - WARNING - 15356 - 9208 - mybro - format_html -  - BeautifulSoup not installed. Using basic HTML formatting.
2025-06-22T20:22:09.708603+1000 - __main__ - 552 - WARNING - 15356 - 9208 - mybro - _format_javascript -  - jsbeautifier not available. Using basic JS formatting.
2025-06-22T20:22:09.709600+1000 - __main__ - 552 - WARNING - 15356 - 9208 - mybro - _format_javascript -  - jsbeautifier not available. Using basic JS formatting.
2025-06-22T20:22:09.710598+1000 - __main__ - 552 - WARNING - 15356 - 9208 - mybro - _format_javascript -  - jsbeautifier not available. Using basic JS formatting.
2025-06-22T20:22:09.711595+1000 - __main__ - 552 - WARNING - 15356 - 9208 - mybro - _format_javascript -  - jsbeautifier not available. Using basic JS formatting.
2025-06-22T20:22:09.712593+1000 - __main__ - 552 - WARNING - 15356 - 9208 - mybro - _format_javascript -  - jsbeautifier not available. Using basic JS formatting.
2025-06-22T20:22:09.713590+1000 - __main__ - 552 - WARNING - 15356 - 9208 - mybro - _format_javascript -  - jsbeautifier not available. Using basic JS formatting.
2025-06-22T20:22:09.713590+1000 - __main__ - 552 - WARNING - 15356 - 9208 - mybro - _format_javascript -  - jsbeautifier not available. Using basic JS formatting.
2025-06-22T20:22:09.714587+1000 - __main__ - 552 - WARNING - 15356 - 9208 - mybro - _format_javascript -  - jsbeautifier not available. Using basic JS formatting.
2025-06-22T20:22:09.715585+1000 - __main__ - 552 - WARNING - 15356 - 9208 - mybro - _format_javascript -  - jsbeautifier not available. Using basic JS formatting.
2025-06-22T20:22:09.715585+1000 - __main__ - 552 - WARNING - 15356 - 9208 - mybro - _format_javascript -  - jsbeautifier not available. Using basic JS formatting.
2025-06-22T20:22:09.715585+1000 - __main__ - 552 - WARNING - 15356 - 9208 - mybro - _format_javascript -  - jsbeautifier not available. Using basic JS formatting.
2025-06-22T20:22:09.716582+1000 - __main__ - 552 - WARNING - 15356 - 9208 - mybro - _format_javascript -  - jsbeautifier not available. Using basic JS formatting.
2025-06-22T20:22:09.717579+1000 - __main__ - 552 - WARNING - 15356 - 9208 - mybro - _format_javascript -  - jsbeautifier not available. Using basic JS formatting.
2025-06-22T20:22:09.718576+1000 - __main__ - 552 - WARNING - 15356 - 9208 - mybro - _format_javascript -  - jsbeautifier not available. Using basic JS formatting.
2025-06-22T20:22:09.718576+1000 - __main__ - 552 - WARNING - 15356 - 9208 - mybro - _format_javascript -  - jsbeautifier not available. Using basic JS formatting.
2025-06-22T20:22:09.718576+1000 - __main__ - 552 - WARNING - 15356 - 9208 - mybro - _format_javascript -  - jsbeautifier not available. Using basic JS formatting.
2025-06-22T20:22:09.719574+1000 - __main__ - 552 - WARNING - 15356 - 9208 - mybro - _format_javascript -  - jsbeautifier not available. Using basic JS formatting.
2025-06-22T20:22:09.781580+1000 - __main__ - 442 - INFO - 15356 - 9208 - mybro - format_html -  - HTML source formatted and highlighted
2025-06-22T20:53:10.402426+1000 - __main__ - 1153 - DEBUG - 436 - 20968 - mybro - updateSourceButton -  - Source button hidden
2025-06-22T20:53:10.403423+1000 - __main__ - 1139 - DEBUG - 436 - 20968 - mybro - tabChanged -  - Switched to tab 0: https://www.google.com
2025-06-22T20:53:10.403423+1000 - __main__ - 1055 - INFO - 436 - 20968 - mybro - addBrowserTab -  - New tab created (index: 0)
2025-06-22T20:53:10.404421+1000 - __main__ - 1129 - INFO - 436 - 20968 - mybro - setupShortcuts -  - Keyboard shortcuts initialized
2025-06-22T20:53:10.404421+1000 - __main__ - 981 - INFO - 436 - 20968 - mybro - __init__ -  - Browser initialized
2025-06-22T20:53:10.937935+1000 - __main__ - 1153 - DEBUG - 436 - 20968 - mybro - updateSourceButton -  - Source button hidden
2025-06-22T20:53:11.646483+1000 - __main__ - 1150 - DEBUG - 436 - 20968 - mybro - updateSourceButton -  - Source button shown and enabled
2025-06-22T20:53:11.646483+1000 - __main__ - 867 - INFO - 436 - 20968 - mybro - handleLoadFinished -  - Page loaded: https://www.google.com/
2025-06-22T20:53:22.965352+1000 - __main__ - 189 - INFO - 436 - 20968 - mybro - _handle_source_result -  - Page source retrieved, length: 192583 characters
2025-06-22T20:53:22.966334+1000 - __main__ - 210 - WARNING - 436 - 20968 - mybro - _handle_source_result -  - Minified or potentially obfuscated HTML detected
2025-06-22T20:53:30.120461+1000 - __main__ - 485 - WARNING - 436 - 20968 - mybro - format_html -  - BeautifulSoup not installed. Using basic HTML formatting.
2025-06-22T20:53:30.121459+1000 - __main__ - 611 - WARNING - 436 - 20968 - mybro - _format_javascript -  - jsbeautifier not available. Using basic JS formatting.
2025-06-22T20:53:30.122456+1000 - __main__ - 611 - WARNING - 436 - 20968 - mybro - _format_javascript -  - jsbeautifier not available. Using basic JS formatting.
2025-06-22T20:53:30.123453+1000 - __main__ - 611 - WARNING - 436 - 20968 - mybro - _format_javascript -  - jsbeautifier not available. Using basic JS formatting.
2025-06-22T20:53:30.124451+1000 - __main__ - 611 - WARNING - 436 - 20968 - mybro - _format_javascript -  - jsbeautifier not available. Using basic JS formatting.
2025-06-22T20:53:30.125448+1000 - __main__ - 611 - WARNING - 436 - 20968 - mybro - _format_javascript -  - jsbeautifier not available. Using basic JS formatting.
2025-06-22T20:53:30.125448+1000 - __main__ - 611 - WARNING - 436 - 20968 - mybro - _format_javascript -  - jsbeautifier not available. Using basic JS formatting.
2025-06-22T20:53:30.126445+1000 - __main__ - 611 - WARNING - 436 - 20968 - mybro - _format_javascript -  - jsbeautifier not available. Using basic JS formatting.
2025-06-22T20:53:30.127443+1000 - __main__ - 611 - WARNING - 436 - 20968 - mybro - _format_javascript -  - jsbeautifier not available. Using basic JS formatting.
2025-06-22T20:53:30.128426+1000 - __main__ - 611 - WARNING - 436 - 20968 - mybro - _format_javascript -  - jsbeautifier not available. Using basic JS formatting.
2025-06-22T20:53:30.128426+1000 - __main__ - 611 - WARNING - 436 - 20968 - mybro - _format_javascript -  - jsbeautifier not available. Using basic JS formatting.
2025-06-22T20:53:30.129424+1000 - __main__ - 611 - WARNING - 436 - 20968 - mybro - _format_javascript -  - jsbeautifier not available. Using basic JS formatting.
2025-06-22T20:53:30.130421+1000 - __main__ - 611 - WARNING - 436 - 20968 - mybro - _format_javascript -  - jsbeautifier not available. Using basic JS formatting.
2025-06-22T20:53:30.131418+1000 - __main__ - 611 - WARNING - 436 - 20968 - mybro - _format_javascript -  - jsbeautifier not available. Using basic JS formatting.
2025-06-22T20:53:30.131418+1000 - __main__ - 611 - WARNING - 436 - 20968 - mybro - _format_javascript -  - jsbeautifier not available. Using basic JS formatting.
2025-06-22T20:53:30.132415+1000 - __main__ - 611 - WARNING - 436 - 20968 - mybro - _format_javascript -  - jsbeautifier not available. Using basic JS formatting.
2025-06-22T20:53:30.132415+1000 - __main__ - 611 - WARNING - 436 - 20968 - mybro - _format_javascript -  - jsbeautifier not available. Using basic JS formatting.
2025-06-22T20:53:30.132415+1000 - __main__ - 611 - WARNING - 436 - 20968 - mybro - _format_javascript -  - jsbeautifier not available. Using basic JS formatting.
2025-06-22T20:53:30.185499+1000 - __main__ - 490 - INFO - 436 - 20968 - mybro - format_html -  - HTML source formatted and highlighted
2025-06-22T21:01:04.428620+1000 - __main__ - 1179 - DEBUG - 23492 - 21352 - mybro - updateSourceButton -  - Source button hidden
2025-06-22T21:01:04.428620+1000 - __main__ - 1165 - DEBUG - 23492 - 21352 - mybro - tabChanged -  - Switched to tab 0: https://www.google.com
2025-06-22T21:01:04.429618+1000 - __main__ - 1081 - INFO - 23492 - 21352 - mybro - addBrowserTab -  - New tab created (index: 0)
2025-06-22T21:01:04.429618+1000 - __main__ - 1155 - INFO - 23492 - 21352 - mybro - setupShortcuts -  - Keyboard shortcuts initialized
2025-06-22T21:01:04.429618+1000 - __main__ - 1007 - INFO - 23492 - 21352 - mybro - __init__ -  - Browser initialized
2025-06-22T21:01:04.987960+1000 - __main__ - 1179 - DEBUG - 23492 - 21352 - mybro - updateSourceButton -  - Source button hidden
2025-06-22T21:01:05.629756+1000 - __main__ - 1176 - DEBUG - 23492 - 21352 - mybro - updateSourceButton -  - Source button shown and enabled
2025-06-22T21:01:05.629756+1000 - __main__ - 893 - INFO - 23492 - 21352 - mybro - handleLoadFinished -  - Page loaded: https://www.google.com/
2025-06-22T21:01:09.256792+1000 - __main__ - 189 - INFO - 23492 - 21352 - mybro - _handle_source_result -  - Page source retrieved, length: 192309 characters
2025-06-22T21:01:09.256792+1000 - __main__ - 210 - WARNING - 23492 - 21352 - mybro - _handle_source_result -  - Minified or potentially obfuscated HTML detected
2025-06-22T21:01:14.502898+1000 - __main__ - 488 - WARNING - 23492 - 21352 - mybro - format_html -  - BeautifulSoup not installed. Using basic HTML formatting.
2025-06-22T21:01:14.503896+1000 - __main__ - 644 - WARNING - 23492 - 21352 - mybro - _format_javascript -  - jsbeautifier not available. Using basic JS formatting.
2025-06-22T21:01:14.504893+1000 - __main__ - 644 - WARNING - 23492 - 21352 - mybro - _format_javascript -  - jsbeautifier not available. Using basic JS formatting.
2025-06-22T21:01:14.505891+1000 - __main__ - 644 - WARNING - 23492 - 21352 - mybro - _format_javascript -  - jsbeautifier not available. Using basic JS formatting.
2025-06-22T21:01:14.507885+1000 - __main__ - 644 - WARNING - 23492 - 21352 - mybro - _format_javascript -  - jsbeautifier not available. Using basic JS formatting.
2025-06-22T21:01:14.508882+1000 - __main__ - 644 - WARNING - 23492 - 21352 - mybro - _format_javascript -  - jsbeautifier not available. Using basic JS formatting.
2025-06-22T21:01:14.508882+1000 - __main__ - 644 - WARNING - 23492 - 21352 - mybro - _format_javascript -  - jsbeautifier not available. Using basic JS formatting.
2025-06-22T21:01:14.509880+1000 - __main__ - 644 - WARNING - 23492 - 21352 - mybro - _format_javascript -  - jsbeautifier not available. Using basic JS formatting.
2025-06-22T21:01:14.509880+1000 - __main__ - 644 - WARNING - 23492 - 21352 - mybro - _format_javascript -  - jsbeautifier not available. Using basic JS formatting.
2025-06-22T21:01:14.510966+1000 - __main__ - 644 - WARNING - 23492 - 21352 - mybro - _format_javascript -  - jsbeautifier not available. Using basic JS formatting.
2025-06-22T21:01:14.510966+1000 - __main__ - 644 - WARNING - 23492 - 21352 - mybro - _format_javascript -  - jsbeautifier not available. Using basic JS formatting.
2025-06-22T21:01:14.511963+1000 - __main__ - 644 - WARNING - 23492 - 21352 - mybro - _format_javascript -  - jsbeautifier not available. Using basic JS formatting.
2025-06-22T21:01:14.511963+1000 - __main__ - 644 - WARNING - 23492 - 21352 - mybro - _format_javascript -  - jsbeautifier not available. Using basic JS formatting.
2025-06-22T21:01:14.513958+1000 - __main__ - 644 - WARNING - 23492 - 21352 - mybro - _format_javascript -  - jsbeautifier not available. Using basic JS formatting.
2025-06-22T21:01:14.513958+1000 - __main__ - 644 - WARNING - 23492 - 21352 - mybro - _format_javascript -  - jsbeautifier not available. Using basic JS formatting.
2025-06-22T21:01:14.513958+1000 - __main__ - 644 - WARNING - 23492 - 21352 - mybro - _format_javascript -  - jsbeautifier not available. Using basic JS formatting.
2025-06-22T21:01:14.514955+1000 - __main__ - 644 - WARNING - 23492 - 21352 - mybro - _format_javascript -  - jsbeautifier not available. Using basic JS formatting.
2025-06-22T21:01:14.514955+1000 - __main__ - 644 - WARNING - 23492 - 21352 - mybro - _format_javascript -  - jsbeautifier not available. Using basic JS formatting.
2025-06-22T21:01:14.584832+1000 - __main__ - 493 - INFO - 23492 - 21352 - mybro - format_html -  - HTML source formatted and highlighted
2025-06-22T21:04:31.961472+1000 - __main__ - 1192 - DEBUG - 12564 - 16496 - mybro - updateSourceButton -  - Source button hidden
2025-06-22T21:04:31.961472+1000 - __main__ - 1178 - DEBUG - 12564 - 16496 - mybro - tabChanged -  - Switched to tab 0: https://www.google.com
2025-06-22T21:04:31.962469+1000 - __main__ - 1094 - INFO - 12564 - 16496 - mybro - addBrowserTab -  - New tab created (index: 0)
2025-06-22T21:04:31.962469+1000 - __main__ - 1168 - INFO - 12564 - 16496 - mybro - setupShortcuts -  - Keyboard shortcuts initialized
2025-06-22T21:04:31.962469+1000 - __main__ - 1020 - INFO - 12564 - 16496 - mybro - __init__ -  - Browser initialized
2025-06-22T21:04:32.457096+1000 - __main__ - 1192 - DEBUG - 12564 - 16496 - mybro - updateSourceButton -  - Source button hidden
2025-06-22T21:04:33.023385+1000 - __main__ - 1189 - DEBUG - 12564 - 16496 - mybro - updateSourceButton -  - Source button shown and enabled
2025-06-22T21:04:33.023385+1000 - __main__ - 906 - INFO - 12564 - 16496 - mybro - handleLoadFinished -  - Page loaded: https://www.google.com/
2025-06-22T21:04:33.845184+1000 - __main__ - 189 - INFO - 12564 - 16496 - mybro - _handle_source_result -  - Page source retrieved, length: 189211 characters
2025-06-22T21:04:33.845184+1000 - __main__ - 210 - WARNING - 12564 - 16496 - mybro - _handle_source_result -  - Minified or potentially obfuscated HTML detected
2025-06-22T21:04:36.860621+1000 - __main__ - 488 - WARNING - 12564 - 16496 - mybro - format_html -  - BeautifulSoup not installed. Using basic HTML formatting.
2025-06-22T21:04:36.948409+1000 - __main__ - 493 - INFO - 12564 - 16496 - mybro - format_html -  - HTML source formatted and highlighted
2025-06-22T21:06:06.948161+1000 - __main__ - 1192 - DEBUG - 7844 - 19160 - mybro - updateSourceButton -  - Source button hidden
2025-06-22T21:06:06.949159+1000 - __main__ - 1178 - DEBUG - 7844 - 19160 - mybro - tabChanged -  - Switched to tab 0: https://www.google.com
2025-06-22T21:06:06.949159+1000 - __main__ - 1094 - INFO - 7844 - 19160 - mybro - addBrowserTab -  - New tab created (index: 0)
2025-06-22T21:06:06.950156+1000 - __main__ - 1168 - INFO - 7844 - 19160 - mybro - setupShortcuts -  - Keyboard shortcuts initialized
2025-06-22T21:06:06.950156+1000 - __main__ - 1020 - INFO - 7844 - 19160 - mybro - __init__ -  - Browser initialized
2025-06-22T21:06:07.476399+1000 - __main__ - 1192 - DEBUG - 7844 - 19160 - mybro - updateSourceButton -  - Source button hidden
2025-06-22T21:06:08.089894+1000 - __main__ - 1189 - DEBUG - 7844 - 19160 - mybro - updateSourceButton -  - Source button shown and enabled
2025-06-22T21:06:08.089894+1000 - __main__ - 906 - INFO - 7844 - 19160 - mybro - handleLoadFinished -  - Page loaded: https://www.google.com/
2025-06-22T21:06:08.453840+1000 - __main__ - 189 - INFO - 7844 - 19160 - mybro - _handle_source_result -  - Page source retrieved, length: 192607 characters
2025-06-22T21:06:08.453840+1000 - __main__ - 210 - WARNING - 7844 - 19160 - mybro - _handle_source_result -  - Minified or potentially obfuscated HTML detected
2025-06-22T21:06:20.948557+1000 - __main__ - 189 - INFO - 7844 - 19160 - mybro - _handle_source_result -  - Page source retrieved, length: 192925 characters
2025-06-22T21:06:20.949554+1000 - __main__ - 210 - WARNING - 7844 - 19160 - mybro - _handle_source_result -  - Minified or potentially obfuscated HTML detected
2025-06-22T21:06:57.128500+1000 - __main__ - 805 - INFO - 7844 - 19160 - mybro - open_in_external_editor -  - Opened source code with QDesktopServices: d:\Code\Python\alan\2nd\temp\www.google.com_20250622_210656.html
2025-06-22T21:15:39.112728+1000 - __main__ - 1119 - INFO - 20692 - 13936 - mybro - addBrowserTab -  - New tab created (index: 0)
2025-06-22T21:15:39.113725+1000 - __main__ - 1193 - INFO - 20692 - 13936 - mybro - setupShortcuts -  - Keyboard shortcuts initialized
2025-06-22T21:15:39.113725+1000 - __main__ - 1007 - INFO - 20692 - 13936 - mybro - __init__ -  - Browser initialized
2025-06-22T21:15:40.229938+1000 - __main__ - 893 - INFO - 20692 - 13936 - mybro - handleLoadFinished -  - Page loaded: https://www.google.com/
2025-06-22T21:15:53.020203+1000 - __main__ - 189 - INFO - 20692 - 13936 - mybro - _handle_source_result -  - Page source retrieved, length: 192446 characters
2025-06-22T21:15:55.753546+1000 - __main__ - 475 - WARNING - 20692 - 13936 - mybro - format_html -  - BeautifulSoup not installed. Using basic HTML formatting.
2025-06-22T21:15:55.836152+1000 - __main__ - 480 - INFO - 20692 - 13936 - mybro - format_html -  - HTML source formatted and highlighted
2025-06-22T21:22:05.061453+1000 - __main__ - 1224 - DEBUG - 21852 - 16888 - mybro - updateSourceButton -  - Source button hidden
2025-06-22T21:22:05.061453+1000 - __main__ - 1207 - DEBUG - 21852 - 16888 - mybro - tabChanged -  - Switched to tab 0: https://www.google.com
2025-06-22T21:22:05.062450+1000 - __main__ - 1123 - INFO - 21852 - 16888 - mybro - addBrowserTab -  - New tab created (index: 0)
2025-06-22T21:22:05.062450+1000 - __main__ - 1197 - INFO - 21852 - 16888 - mybro - setupShortcuts -  - Keyboard shortcuts initialized
2025-06-22T21:22:05.062450+1000 - __main__ - 1007 - INFO - 21852 - 16888 - mybro - __init__ -  - Browser initialized
2025-06-22T21:22:05.587207+1000 - __main__ - 1224 - DEBUG - 21852 - 16888 - mybro - updateSourceButton -  - Source button hidden
2025-06-22T21:22:06.388563+1000 - __main__ - 1220 - DEBUG - 21852 - 16888 - mybro - updateSourceButton -  - Source button shown and enabled
2025-06-22T21:22:06.388563+1000 - __main__ - 893 - INFO - 21852 - 16888 - mybro - handleLoadFinished -  - Page loaded: https://www.google.com/
2025-06-22T21:22:12.784364+1000 - __main__ - 1185 - DEBUG - 21852 - 16888 - mybro - reloadPage -  - Reloading page
2025-06-22T21:22:13.615419+1000 - __main__ - 1220 - DEBUG - 21852 - 16888 - mybro - updateSourceButton -  - Source button shown and enabled
2025-06-22T21:22:13.615419+1000 - __main__ - 893 - INFO - 21852 - 16888 - mybro - handleLoadFinished -  - Page loaded: https://www.google.com/
2025-06-22T21:22:22.508363+1000 - __main__ - 1253 - INFO - 21852 - 16888 - mybro - navigateToUrl -  - Loading URL: https://www.amazon.com
2025-06-22T21:22:23.234977+1000 - __main__ - 1224 - DEBUG - 21852 - 16888 - mybro - updateSourceButton -  - Source button hidden
2025-06-22T21:22:25.227120+1000 - __main__ - 1220 - DEBUG - 21852 - 16888 - mybro - updateSourceButton -  - Source button shown and enabled
2025-06-22T21:22:25.227120+1000 - __main__ - 893 - INFO - 21852 - 16888 - mybro - handleLoadFinished -  - Page loaded: https://www.amazon.com.au/?ref_=mr_direct_us_au_au&showmri
2025-06-22T21:22:29.063528+1000 - __main__ - 189 - INFO - 21852 - 16888 - mybro - _handle_source_result -  - Page source retrieved, length: 1272563 characters
2025-06-22T21:22:34.315815+1000 - __main__ - 475 - WARNING - 21852 - 16888 - mybro - format_html -  - BeautifulSoup not installed. Using basic HTML formatting.
2025-06-22T21:22:34.429511+1000 - __main__ - 480 - INFO - 21852 - 16888 - mybro - format_html -  - HTML source formatted and highlighted
2025-06-22T21:26:00.751776+1000 - __main__ - 1221 - DEBUG - 21168 - 17804 - mybro - updateSourceButton -  - Source button hidden
2025-06-22T21:26:00.752774+1000 - __main__ - 1204 - DEBUG - 21168 - 17804 - mybro - tabChanged -  - Switched to tab 0: https://www.google.com
2025-06-22T21:26:00.753770+1000 - __main__ - 1120 - INFO - 21168 - 17804 - mybro - addBrowserTab -  - New tab created (index: 0)
2025-06-22T21:26:00.754768+1000 - __main__ - 1194 - INFO - 21168 - 17804 - mybro - setupShortcuts -  - Keyboard shortcuts initialized
2025-06-22T21:26:00.754768+1000 - __main__ - 1007 - INFO - 21168 - 17804 - mybro - __init__ -  - Browser initialized
2025-06-22T21:26:01.331947+1000 - __main__ - 1221 - DEBUG - 21168 - 17804 - mybro - updateSourceButton -  - Source button hidden
2025-06-22T21:26:01.918260+1000 - __main__ - 1217 - DEBUG - 21168 - 17804 - mybro - updateSourceButton -  - Source button shown and enabled
2025-06-22T21:26:01.918260+1000 - __main__ - 893 - INFO - 21168 - 17804 - mybro - handleLoadFinished -  - Page loaded: https://www.google.com/
2025-06-22T21:30:01.561349+1000 - __main__ - 1016 - INFO - 21588 - 23288 - mybro - positionOnMainScreen -  - Detected 2 screen(s)
2025-06-22T21:30:01.561349+1000 - __main__ - 1020 - INFO - 21588 - 23288 - mybro - positionOnMainScreen -  - Multiple monitors detected, positioning on primary screen
2025-06-22T21:30:01.677643+1000 - __main__ - 1043 - INFO - 21588 - 23288 - mybro - positionOnMainScreen -  - Window maximized on primary screen
2025-06-22T21:30:02.308101+1000 - __main__ - 1261 - DEBUG - 21588 - 23288 - mybro - updateSourceButton -  - Source button hidden
2025-06-22T21:30:02.308101+1000 - __main__ - 1244 - DEBUG - 21588 - 23288 - mybro - tabChanged -  - Switched to tab 0: https://www.google.com
2025-06-22T21:30:02.309098+1000 - __main__ - 1160 - INFO - 21588 - 23288 - mybro - addBrowserTab -  - New tab created (index: 0)
2025-06-22T21:30:02.310096+1000 - __main__ - 1234 - INFO - 21588 - 23288 - mybro - setupShortcuts -  - Keyboard shortcuts initialized
2025-06-22T21:30:08.377507+1000 - __main__ - 1016 - INFO - 17920 - 9980 - mybro - positionOnMainScreen -  - Detected 2 screen(s)
2025-06-22T21:30:08.377507+1000 - __main__ - 1020 - INFO - 17920 - 9980 - mybro - positionOnMainScreen -  - Multiple monitors detected, positioning on primary screen
2025-06-22T21:30:08.482766+1000 - __main__ - 1043 - INFO - 17920 - 9980 - mybro - positionOnMainScreen -  - Window maximized on primary screen
2025-06-22T21:30:09.084605+1000 - __main__ - 1261 - DEBUG - 17920 - 9980 - mybro - updateSourceButton -  - Source button hidden
2025-06-22T21:30:09.084605+1000 - __main__ - 1244 - DEBUG - 17920 - 9980 - mybro - tabChanged -  - Switched to tab 0: https://www.google.com
2025-06-22T21:30:09.085603+1000 - __main__ - 1160 - INFO - 17920 - 9980 - mybro - addBrowserTab -  - New tab created (index: 0)
2025-06-22T21:30:09.086600+1000 - __main__ - 1234 - INFO - 17920 - 9980 - mybro - setupShortcuts -  - Keyboard shortcuts initialized
2025-06-22T21:33:06.530911+1000 - __main__ - 963 - INFO - 14860 - 22868 - mybro - positionOnMainScreen -  - Detected 2 screen(s)
2025-06-22T21:33:06.530911+1000 - __main__ - 967 - INFO - 14860 - 22868 - mybro - positionOnMainScreen -  - Multiple monitors detected, positioning on primary screen
2025-06-22T21:33:06.636860+1000 - __main__ - 990 - INFO - 14860 - 22868 - mybro - positionOnMainScreen -  - Window maximized on primary screen
2025-06-22T21:33:07.237332+1000 - __main__ - 1208 - DEBUG - 14860 - 22868 - mybro - updateSourceButton -  - Source button hidden
2025-06-22T21:33:07.237332+1000 - __main__ - 1191 - DEBUG - 14860 - 22868 - mybro - tabChanged -  - Switched to tab 0: https://www.google.com
2025-06-22T21:33:07.238330+1000 - __main__ - 1107 - INFO - 14860 - 22868 - mybro - addBrowserTab -  - New tab created (index: 0)
2025-06-22T21:33:07.238330+1000 - __main__ - 1181 - INFO - 14860 - 22868 - mybro - setupShortcuts -  - Keyboard shortcuts initialized
2025-06-22T21:38:53.179926+1000 - __main__ - 1377 - INFO - 21252 - 14704 - mybro - main -  - Total screens: 2
2025-06-22T21:38:53.179926+1000 - __main__ - 1379 - INFO - 21252 - 14704 - mybro - main -  - Primary screen: DELL P2719H (1)
2025-06-22T21:38:53.179926+1000 - __main__ - 1383 - INFO - 21252 - 14704 - mybro - main -  - Screen 0: DELL P2719H (1) PRIMARY
2025-06-22T21:38:53.179926+1000 - __main__ - 1384 - INFO - 21252 - 14704 - mybro - main -  -   - Size: 1920x1080
2025-06-22T21:38:53.179926+1000 - __main__ - 1385 - INFO - 21252 - 14704 - mybro - main -  -   - Available geometry: 1920x1040
2025-06-22T21:38:53.179926+1000 - __main__ - 1383 - INFO - 21252 - 14704 - mybro - main -  - Screen 1: DELL P2719H (2) 
2025-06-22T21:38:53.179926+1000 - __main__ - 1384 - INFO - 21252 - 14704 - mybro - main -  -   - Size: 1080x1920
2025-06-22T21:38:53.179926+1000 - __main__ - 1385 - INFO - 21252 - 14704 - mybro - main -  -   - Available geometry: 1080x1880
2025-06-22T21:38:53.179926+1000 - __main__ - 929 - INFO - 21252 - 14704 - mybro - __init__ -  - Detected 2 screen(s)
2025-06-22T21:38:53.179926+1000 - __main__ - 933 - INFO - 21252 - 14704 - mybro - __init__ -  - Multiple monitors detected, positioning on primary screen
2025-06-22T21:38:53.305199+1000 - __main__ - 956 - INFO - 21252 - 14704 - mybro - __init__ -  - Window maximized on primary screen
2025-06-22T21:38:53.948309+1000 - __main__ - 1240 - DEBUG - 21252 - 14704 - mybro - updateSourceButton -  - Source button hidden
2025-06-22T21:38:53.948309+1000 - __main__ - 1223 - DEBUG - 21252 - 14704 - mybro - tabChanged -  - Switched to tab 0: https://www.google.com
2025-06-22T21:38:53.948309+1000 - __main__ - 1139 - INFO - 21252 - 14704 - mybro - addBrowserTab -  - New tab created (index: 0)
2025-06-22T21:38:53.949308+1000 - __main__ - 1213 - INFO - 21252 - 14704 - mybro - setupShortcuts -  - Keyboard shortcuts initialized
2025-06-22T21:38:54.435571+1000 - __main__ - 1240 - DEBUG - 21252 - 14704 - mybro - updateSourceButton -  - Source button hidden
2025-06-22T21:38:55.298604+1000 - __main__ - 1236 - DEBUG - 21252 - 14704 - mybro - updateSourceButton -  - Source button shown and enabled
2025-06-22T21:38:55.298604+1000 - __main__ - 893 - INFO - 21252 - 14704 - mybro - handleLoadFinished -  - Page loaded: https://www.google.com/
2025-06-22T21:42:34.038027+1000 - __main__ - 1382 - INFO - 6036 - 23112 - mybro - main -  - Total screens: 2
2025-06-22T21:42:34.038027+1000 - __main__ - 1384 - INFO - 6036 - 23112 - mybro - main -  - Primary screen: DELL P2719H (1)
2025-06-22T21:42:34.038027+1000 - __main__ - 1388 - INFO - 6036 - 23112 - mybro - main -  - Screen 0: DELL P2719H (1) PRIMARY
2025-06-22T21:42:34.038027+1000 - __main__ - 1389 - INFO - 6036 - 23112 - mybro - main -  -   - Size: 1920x1080
2025-06-22T21:42:34.039024+1000 - __main__ - 1390 - INFO - 6036 - 23112 - mybro - main -  -   - Available geometry: 1920x1040
2025-06-22T21:42:34.039024+1000 - __main__ - 1388 - INFO - 6036 - 23112 - mybro - main -  - Screen 1: DELL P2719H (2) 
2025-06-22T21:42:34.039024+1000 - __main__ - 1389 - INFO - 6036 - 23112 - mybro - main -  -   - Size: 1080x1920
2025-06-22T21:42:34.039024+1000 - __main__ - 1390 - INFO - 6036 - 23112 - mybro - main -  -   - Available geometry: 1080x1880
2025-06-22T21:42:34.039024+1000 - __main__ - 929 - INFO - 6036 - 23112 - mybro - __init__ -  - Detected 2 screen(s)
2025-06-22T21:42:34.039024+1000 - __main__ - 933 - INFO - 6036 - 23112 - mybro - __init__ -  - Multiple monitors detected, positioning on primary screen
2025-06-22T21:42:34.156310+1000 - __main__ - 956 - INFO - 6036 - 23112 - mybro - __init__ -  - Window maximized on primary screen
2025-06-22T21:42:34.767874+1000 - __main__ - 1245 - DEBUG - 6036 - 23112 - mybro - updateSourceButton -  - Source button hidden
2025-06-22T21:42:34.768871+1000 - __main__ - 1228 - DEBUG - 6036 - 23112 - mybro - tabChanged -  - Switched to tab 0: https://www.google.com
2025-06-22T21:42:34.768871+1000 - __main__ - 1144 - INFO - 6036 - 23112 - mybro - addBrowserTab -  - New tab created (index: 0)
2025-06-22T21:42:34.768871+1000 - __main__ - 1218 - INFO - 6036 - 23112 - mybro - setupShortcuts -  - Keyboard shortcuts initialized
2025-06-22T21:42:35.273805+1000 - __main__ - 1245 - DEBUG - 6036 - 23112 - mybro - updateSourceButton -  - Source button hidden
2025-06-22T21:42:35.914214+1000 - __main__ - 1241 - DEBUG - 6036 - 23112 - mybro - updateSourceButton -  - Source button shown and enabled
2025-06-22T21:42:35.914214+1000 - __main__ - 893 - INFO - 6036 - 23112 - mybro - handleLoadFinished -  - Page loaded: https://www.google.com/

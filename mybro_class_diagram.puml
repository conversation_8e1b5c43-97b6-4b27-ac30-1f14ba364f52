@startuml MyBro Browser Class Diagram
!theme plain
skinparam backgroundColor #FFFFFF
skinparam class {
  BackgroundColor #E3F2FD
  BorderColor #1976D2
  ArrowColor #1976D2
}
skinparam interface {
  BackgroundColor #F3E5F5
  BorderColor #7B1FA2
}

title MyBro Browser - Class Structure

package "Qt Framework" {
  class QMainWindow
  class QWidget
  class QLineEdit
  class QToolButton
  class QWebEngineView
  class QTabWidget
  class QDialog
  class QTextEdit
}

package "External Libraries" {
  class QsciScintilla
  class BeautifulSoup
  note top of QsciScintilla : Optional dependency\nfor syntax highlighting
  note top of BeautifulSoup : Optional dependency\nfor HTML formatting
}

package "MyBro Application" {
  
  class MyBrowser {
    -central: QWidget
    -layout: QVBoxLayout
    -tabs: QTabWidget
    -status: QStatusBar
    -address_bar: AddressBar
    -source_button: QToolButton
    -user_button: QToolButton
    -system_button: SystemMenuButton
    +__init__()
    +setupToolbar()
    +addBrowserTab(url=None): BrowserTab
    +closeTab(index: int)
    +currentTab(): BrowserTab
    +viewPageSource()
    +navigateToUrl()
    +goBack()
    +goForward()
    +reloadPage()
    +goHome()
    +setupShortcuts()
    +updateAddressBar(url: QUrl)
    +updateTabTitle(title: str)
    +updateSourceButton(loaded: bool)
  }
  
  class BrowserTab {
    -layout: QVBoxLayout
    -webview: QWebEngineView
    -is_page_loaded: bool
    -current_page_source: str
    +titleChanged: Signal
    +urlChanged: Signal
    +loadFinished: Signal
    +__init__()
    +back()
    +forward()
    +reload()
    +stop()
    +load(url: QUrl)
    +current_title(): str
    +current_url(): QUrl
    +get_page_source()
    +show_source_viewer()
    +format_html(text_edit: QTextEdit)
    +format_html_with_qscintilla(editor)
    +save_source_to_file()
    +open_in_external_editor()
    -_handle_source_result(html: str)
    -_is_minified_or_obfuscated(html: str): bool
    -_ensure_balanced_tags(html: str): str
    -_apply_basic_html_formatting(html: str): str
    -_format_html_with_proper_indentation(html: str): str
    -_format_javascript(js_code: str): str
    -_format_css(css_code: str): str
  }
  
  class AddressBar {
    +__init__(parent=None)
    +setPlaceholderText(text: str)
    +setStyleSheet(style: str)
  }
  
  class SystemMenuButton {
    +__init__(parent=None)
    +setMenu(menu: QMenu)
  }
  
  class ProfileButton {
    -menu: QMenu
    +__init__(parent=None)
    +setMenu(menu: QMenu)
  }
  
}

' Inheritance relationships
MyBrowser --|> QMainWindow
BrowserTab --|> QWidget
AddressBar --|> QLineEdit
SystemMenuButton --|> QToolButton
ProfileButton --|> QToolButton

' Composition relationships
MyBrowser *-- BrowserTab : contains multiple
MyBrowser *-- AddressBar : contains one
MyBrowser *-- SystemMenuButton : contains one
MyBrowser *-- QTabWidget : contains one
BrowserTab *-- QWebEngineView : contains one

' Dependencies
BrowserTab ..> QsciScintilla : uses if available
BrowserTab ..> BeautifulSoup : uses if available
BrowserTab ..> QDialog : creates for source viewing
BrowserTab ..> QTextEdit : creates for source display

' Notes
note right of MyBrowser
  Main application window
  Manages tabs, toolbar, and navigation
  Handles keyboard shortcuts
  Supports multi-monitor setup
end note

note right of BrowserTab
  Individual browser tab
  Contains QWebEngineView
  Handles page source extraction
  Supports multiple formatting methods
  Manages JavaScript execution
end note

note right of AddressBar
  Custom styled URL input
  Rounded corners design
  Focus state styling
  Placeholder text support
end note

note bottom of BrowserTab
  Source Code Processing Pipeline:
  1. JavaScript execution to get HTML
  2. Size validation (>5MB warning)
  3. Format selection (QScintilla/BeautifulSoup/Basic)
  4. Display in dialog with options
  5. Save/export functionality
end note

@enduml

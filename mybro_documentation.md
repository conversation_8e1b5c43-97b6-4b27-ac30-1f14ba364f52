# MyBro Browser - Software Documentation

## Overview

MyBro is a modern web browser application built with PySide6 (Qt6) that provides tabbed browsing, advanced source code viewing, and multi-monitor support. The application follows object-oriented design principles and implements a clean separation of concerns.

## Architecture Overview

```plantuml
@startuml Architecture Overview
!theme plain
skinparam packageStyle rectangle

package "Presentation Layer" {
  [MyBrowser MainWindow]
  [AddressBar]
  [Toolbar Components]
  [Tab Management UI]
}

package "Business Logic Layer" {
  [BrowserTab Controller]
  [Navigation Handler]
  [Source Code Processor]
  [Tab Manager]
}

package "Data Layer" {
  [HTML Source Data]
  [Browser History]
  [User Preferences]
}

package "External Dependencies" {
  [Qt WebEngine]
  [QScintilla (Optional)]
  [BeautifulSoup (Optional)]
  [Loguru Logger]
}

[MyBrowser MainWindow] --> [BrowserTab Controller]
[BrowserTab Controller] --> [Qt WebEngine]
[Source Code Processor] --> [QScintilla (Optional)]
[Source Code Processor] --> [BeautifulSoup (Optional)]
[Navigation Handler] --> [Browser History]

@enduml
```

## System Flow Diagram

```plantuml
@startuml System Flow
!theme plain
skinparam activity {
  BackgroundColor #E8F4FD
  BorderColor #1976D2
}

title MyBro Browser - Main System Flow

start

:Initialize Application <<procedure>>;
:Create Main Window <<procedure>>;
:Setup Multi-Monitor Support <<procedure>>;

fork
  :Initialize Toolbar <<procedure>>;
fork again
  :Initialize Tab Widget <<procedure>>;
fork again
  :Setup Keyboard Shortcuts <<procedure>>;
fork again
  :Configure Logging System <<procedure>>;
end fork

:Create First Browser Tab;
:Load Default Page (Google);

repeat
  :Wait for User Input;
  
  switch (User Action?)
  case (URL Navigation)
    :Parse URL Input;
    :Validate URL Format;
    :Load Page in Current Tab;
    
  case (Tab Management)
    switch (Tab Operation?)
    case (New Tab)
      :Create BrowserTab Instance;
      :Add to Tab Widget;
    case (Close Tab)
      if (Last Tab?) then (yes)
        :Reset to Blank Page;
      else (no)
        :Remove Tab Widget;
      endif
    case (Switch Tab)
      :Update UI State;
    endswitch
    
  case (View Source)
    :Check Page Load Status;
    if (Page Loaded?) then (yes)
      :Execute JavaScript Extraction;
      :Validate Source Size;
      :Apply Formatting;
      :Display Source Viewer;
    else (no)
      :Show Error Message;
    endif
    
  case (Browser Navigation)
    switch (Navigation Type?)
    case (Back)
      :webview.back();
    case (Forward)
      :webview.forward();
    case (Reload)
      :webview.reload();
    case (Home)
      :Load Home Page;
    endswitch
    
  endswitch
  
repeat while (Application Active?)

:Cleanup Resources;
stop

@enduml
```

## Class Relationship Diagram

```plantuml
@startuml Class Relationships
!theme plain

title MyBro Browser - Class Structure

class MyBrowser {
  -tabs: QTabWidget
  -address_bar: AddressBar
  -source_button: QToolButton
  +setupToolbar()
  +addBrowserTab(): BrowserTab
  +viewPageSource()
  +navigateToUrl()
  +updateSourceButton(loaded: bool)
}

class BrowserTab {
  -webview: QWebEngineView
  -is_page_loaded: bool
  -current_page_source: str
  +get_page_source()
  +show_source_viewer()
  +format_html()
  +save_source_to_file()
}

class AddressBar {
  +setPlaceholderText()
  +setStyleSheet()
}

class SourceCodeProcessor {
  +format_html_with_qscintilla()
  +format_html_with_beautifulsoup()
  +apply_basic_formatting()
  +ensure_balanced_tags()
}

MyBrowser ||--o{ BrowserTab : manages
MyBrowser ||--|| AddressBar : contains
BrowserTab ||--|| QWebEngineView : contains
BrowserTab ..> SourceCodeProcessor : uses

@enduml
```

## Source Code Processing Flow

```plantuml
@startuml Source Processing
!theme plain

title Source Code Processing Pipeline

start

:User Clicks Source Button;
:Check Page Load Status;

if (Page Loaded?) then (no)
  :Display Warning Message;
  stop
else (yes)
  :Execute JavaScript;
  note right: document.documentElement.outerHTML
endif

:Retrieve HTML Content;
:Check Source Size;

if (Size > 5MB?) then (yes)
  :Show Large File Warning;
  if (User Continues?) then (no)
    stop
  endif
endif

:Create Source Viewer Dialog;

switch (Formatting Method Available?)
case (QScintilla)
  :Setup QScintilla Editor <<procedure>>;
  :Apply HTML Lexer;
  :Enable Syntax Highlighting;
  :Add Line Numbers;
  
case (BeautifulSoup)
  :Parse HTML with BeautifulSoup <<procedure>>;
  :Apply Pretty Print;
  :Add Line Numbers;
  :Display in QTextEdit;
  
case (Basic Formatter)
  :Apply Manual Formatting <<procedure>>;
  :Handle Script/Style Blocks;
  :Add Proper Indentation;
  :Add Line Numbers;
  
endswitch

:Display Formatted Source;
:Provide Save/Export Options;

repeat
  :Wait for User Action;
  switch (Dialog Action?)
  case (Format)
    :Reapply Formatting;
  case (Save)
    :Save to File System;
    :Show Success Message;
  case (Close)
    :Close Dialog;
  endswitch
repeat while (Dialog Open?)

stop

@enduml
```

## Key Features

### 1. Multi-Tab Browsing
- Dynamic tab creation and management
- Tab close protection (prevents closing last tab)
- Tab state synchronization with UI components

### 2. Advanced Source Code Viewing
- **Multiple Formatting Engines**:
  - QScintilla: Professional syntax highlighting
  - BeautifulSoup: HTML pretty printing
  - Basic Formatter: Fallback manual formatting
- **Security Features**:
  - Large file detection (>5MB warning)
  - Obfuscation detection
  - Safe JavaScript execution

### 3. Multi-Monitor Support
- Automatic primary screen detection
- Intelligent window positioning
- Maximized display on multiple monitors

### 4. User Experience
- Custom styled address bar
- Keyboard shortcuts for power users
- Comprehensive error handling
- Detailed logging system

## Technical Specifications

### Dependencies
- **Required**: PySide6, loguru
- **Optional**: PyQt6-QScintilla, beautifulsoup4, jsbeautifier

### Logging Configuration
- File: `logs/mybro.log`
- Rotation: Daily at midnight
- Retention: 7 days
- Compression: ZIP format

### Keyboard Shortcuts
- `Ctrl+T`: New Tab
- `Ctrl+W`: Close Tab
- `Ctrl+L`: Focus Address Bar
- `Ctrl+U`: View Page Source
- `F5`: Reload Page
- `Alt+Left/Right`: Navigate Back/Forward

## Error Handling Strategy

The application implements comprehensive error handling:
- Graceful degradation for optional dependencies
- User-friendly error messages
- Detailed logging for debugging
- Fallback mechanisms for critical features

## Sequence Diagram - Source Code Viewing

```plantuml
@startuml Source Viewing Sequence
!theme plain

title Source Code Viewing - Interaction Sequence

actor User
participant "MyBrowser" as MB
participant "BrowserTab" as BT
participant "QWebEngineView" as WV
participant "SourceDialog" as SD
participant "Formatter" as FMT

User -> MB: Click Source Button
MB -> BT: viewPageSource()
BT -> BT: check is_page_loaded

alt Page Not Loaded
    BT -> MB: Show Warning
    MB -> User: Display Error Message
else Page Loaded
    BT -> WV: runJavaScript()
    note right: document.documentElement.outerHTML
    WV -> BT: _handle_source_result(html)

    BT -> BT: Check source size
    alt Large File (>5MB)
        BT -> User: Show Warning Dialog
        User -> BT: User Choice
        alt User Cancels
            BT -> MB: Return
        end
    end

    BT -> SD: Create Source Dialog
    BT -> FMT: Determine formatting method

    alt QScintilla Available
        FMT -> SD: Setup QScintilla Editor
        FMT -> SD: Apply HTML Lexer
        FMT -> SD: Enable Syntax Highlighting
    else BeautifulSoup Available
        FMT -> FMT: soup.prettify()
        FMT -> SD: Display in QTextEdit
    else Basic Formatting
        FMT -> FMT: apply_basic_html_formatting()
        FMT -> SD: Display formatted HTML
    end

    SD -> User: Show Source Dialog

    loop User Interactions
        User -> SD: Dialog Action
        alt Format Button
            SD -> FMT: Reformat HTML
            FMT -> SD: Update Display
        else Save Button
            SD -> BT: save_source_to_file()
            BT -> BT: Write to filesystem
            BT -> User: Show Success Message
        else Close Button
            SD -> SD: Close Dialog
        end
    end
end

@enduml
```

## Component Interaction Diagram

```plantuml
@startuml Component Interactions
!theme plain

title MyBro Browser - Component Interactions

package "UI Layer" {
  component [Toolbar] as TB
  component [AddressBar] as AB
  component [TabWidget] as TW
  component [StatusBar] as SB
}

package "Control Layer" {
  component [MyBrowser Controller] as MBC
  component [BrowserTab Controller] as BTC
  component [Navigation Handler] as NH
}

package "Service Layer" {
  component [Source Processor] as SP
  component [File Manager] as FM
  component [Logger Service] as LS
}

package "External" {
  component [Qt WebEngine] as QWE
  component [File System] as FS
  component [Optional Libraries] as OL
}

TB --> MBC : user actions
AB --> MBC : URL input
TW --> MBC : tab events
MBC --> BTC : tab management
BTC --> NH : navigation requests
BTC --> SP : source processing
BTC --> QWE : web content
SP --> OL : formatting
SP --> FM : file operations
FM --> FS : save/load
LS --> FS : log files

@enduml
```

## Future Enhancements

1. **Bookmarks Management**
2. **Download Manager**
3. **Privacy/Incognito Mode**
4. **Extension System**
5. **Theme Customization**

## Development Guidelines

### Code Style

- Follow PEP 8 Python style guidelines
- Use type hints where applicable
- Maintain comprehensive docstrings
- Implement proper error handling

### Testing Strategy

- Unit tests for core functionality
- Integration tests for UI components
- Performance tests for large file handling
- Cross-platform compatibility testing

### Documentation Standards

- Keep PlantUML diagrams updated with code changes
- Document all public APIs
- Maintain changelog for releases
- Provide setup and deployment instructions

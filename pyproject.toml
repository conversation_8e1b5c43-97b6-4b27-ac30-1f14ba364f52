/*
 * File: d:\Code\Python\alan\2nd\pyproject.toml
 * Project: d:\Code\Python\alan\2nd
 * Created Date: Saturday, June 21st 2025, 07:23:30
 * Author: Jeremy
 * -----
 * Last Modified: Sunday, June 22nd 2025, 15:15:52
 * Modified By: Jeremy
 * -----
 * Copyright (c) 2025 None
 * 
 * Project Description
 * -----
 * HISTORY:
 * Date      	By	Comments
 * ----------	---	----------------------------------------------------------
 */






[tool.black]
line-length = 88
target-version = ['py311']
include = '\.pyi?$'
exclude = '''
/(
    \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | _build
  | buck-out
  | build
  | dist
)/
'''



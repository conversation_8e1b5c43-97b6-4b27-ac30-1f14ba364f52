```plantuml
@startuml MyBro Browser Application Flow
!theme plain
skinparam backgroundColor #FFFFFF
skinparam defaultFontSize 10
skinparam activity {
  BackgroundColor #E8F4FD
  BorderColor #1976D2
  FontColor #000000
}
skinparam decision {
  BackgroundColor #FFF3E0
  BorderColor #F57C00
}
skinparam note {
  BackgroundColor #F1F8E9
  BorderColor #388E3C
}

title MyBro Browser Application - Complete Flow Diagram

start

:Application Start;
:Create MyBrowser Main Window;

fork
  :Setup Window Position|
  if (Multiple Monitors?) then (yes)
    :Position on Primary Screen;
    :Maximize Window;
  else (no)
    :Maximize on Single Monitor;
  endif
fork again
  :Setup Toolbar|
  :Create Back Button;
  :Create Forward Button;
  :Create Reload Button;
  :Create Home Button;
  :Create Address Bar;
  :Create Source Button (Disabled);
  :Create User Menu Button;
  :Create System Menu Button;
fork again
  :Create Tab Widget|
  :Setup Tab Close Handling;
  :Setup Tab Change Handling;
fork again
  :Create Status Bar|
fork again
  :Setup Keyboard Shortcuts|
  :Ctrl+T (New Tab);
  :Ctrl+W (Close Tab);
  :Ctrl+L (Focus Address);
  :Ctrl+U (View Source);
  :F5 (Reload);
  :Alt+Left/Right (Navigation);
end fork

:Add First Browser Tab;
:Create BrowserTab Widget;
:Create QWebEngineView;
:Load Default URL (Google);

repeat
  :Wait for User Action;

  if (Action Type?) then (Navigate)
    :Get Address Bar Input;
    if (URL has scheme?) then (no)
      :Add https:// prefix;
    endif
    :Load URL in Current Tab;

  elseif (Tab Management) then
    if (Tab Action?) then (New Tab)
      :Create New BrowserTab;
      :Add to Tab Widget;
      :Set as Current Tab;
    elseif (Close Tab) then
      if (Last Tab?) then (yes)
        :Load Blank Page;
      else (no)
        :Remove Tab from Widget;
      endif
    elseif (Switch Tab) then
      :Update Address Bar;
      :Update Window Title;
      :Update Source Button State;
    endif

  elseif (View Source) then
    if (Page Loaded?) then (yes)
      :Execute JavaScript to Get HTML;
      :document.documentElement.outerHTML;

      if (Source Size > 5MB?) then (yes)
        :Show Large File Warning;
        if (User Continues?) then (no)
          :Return to Main Loop;
        endif
      endif

      :Create Source Viewer Dialog;

      if (Formatting Method?) then (QScintilla Available)
        :Setup QScintilla Editor;
        :Apply HTML Lexer;
        :Enable Syntax Highlighting;
        :Add Line Numbers;
      elseif (BeautifulSoup Available) then
        :Use BeautifulSoup.prettify();
        :Add Line Numbers;
        :Display in QTextEdit;
      else (Basic Formatting)
        :Apply Manual HTML Formatting;
        :Add Proper Indentation;
        :Handle Script/Style Blocks;
        :Add Line Numbers;
      endif

      :Show Format Button;
      :Show Save Button;
      :Show Close Button;
      :Display Source Dialog;

      repeat
        :Wait for Dialog Action;
        if (Dialog Action?) then (Format)
          :Apply Selected Formatting;
        elseif (Save) then
          :Create temp directory;
          :Generate filename with timestamp;
          :Save HTML to file;
          :Show success message;
        elseif (Close) then
          :Close Dialog;
        endif
      repeat while (Dialog Open?)

    else (no)
      :Show "Page Not Loaded" Warning;
    endif

  elseif (Navigation) then
    if (Navigation Type?) then (Back)
      :webview.back();
    elseif (Forward) then
      :webview.forward();
    elseif (Reload) then
      :webview.reload();
    elseif (Home) then
      :Load Google.com;
    endif
  endif

  note right
    Page Load Events:
    - URL Changed -> Reset load state
    - Load Started -> Disable source button
    - Load Finished -> Enable source button
  end note

repeat while (Application Running?)

:Application Exit;
stop

@enduml
```
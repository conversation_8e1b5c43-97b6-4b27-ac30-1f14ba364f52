@startuml MyBro Browser Application Flow
!theme plain
skinparam backgroundColor #FFFFFF
skinparam defaultFontSize 10

title MyBro Browser Application Architecture & Flow

package "Application Initialization" {
  [Application Start] as start
  [MyBrowser Main Window] as main
  [Setup Window Position] as position
  [Setup Toolbar] as toolbar
  [Create Tab Widget] as tabs
  [Create Status Bar] as status
  [Setup Shortcuts] as shortcuts
}

package "Toolbar Components" {
  [Back Button] as back
  [Forward Button] as forward
  [Reload Button] as reload
  [Home Button] as home
  [Address Bar] as address
  [Source Button] as source
  [User Button] as user
  [System Button] as system
}

package "Tab Management" {
  [BrowserTab Widget] as tab
  [QWebEngineView] as webview
  [Load Default URL] as defaulturl
  [Page Load Event] as loadEvent
}

package "Source Code Viewing" {
  [Get Page Source] as getSource
  [JavaScript Execution] as jsExec
  [Retrieve HTML] as retrieveHTML
  [Source Size Check] as sizeCheck
  [Show Warning Dialog] as warning
  [Show Source Viewer] as viewer
  [Source Dialog] as dialog
  [Format HTML Options] as formatOptions
}

package "Formatting Methods" {
  [QScintilla Syntax] as qscintilla
  [BeautifulSoup Pretty] as beautifulsoup
  [Basic Formatting] as basic
  [Display Formatted Source] as display
  [Save/Export Options] as saveExport
}

package "Navigation Actions" {
  [Address Bar Input] as addressInput
  [Parse URL] as parseURL
  [Load New Page] as loadPage
  [Browser Navigation] as browserNav
}

package "User Interactions" {
  [User Actions] as userActions
  [Tab Operations] as tabOps
  [New Tab] as newTab
  [Close Tab] as closeTab
  [Switch Tab] as switchTab
  [Update UI State] as updateUI
}

' Main flow connections
start --> main
main --> position
main --> toolbar
main --> tabs
main --> status
main --> shortcuts

toolbar --> back
toolbar --> forward
toolbar --> reload
toolbar --> home
toolbar --> address
toolbar --> source
toolbar --> user
toolbar --> system

tabs --> tab
tab --> webview
webview --> defaulturl
defaulturl --> loadEvent

loadEvent --> userActions : "Success"
loadEvent --> userActions : "Failure"

userActions --> addressInput : "Navigate"
userActions --> getSource : "View Source"
userActions --> tabOps : "Tab Management"
userActions --> browserNav : "Navigation"

' Source viewing flow
getSource --> jsExec
jsExec --> retrieveHTML
retrieveHTML --> sizeCheck
sizeCheck --> warning : "Large File"
sizeCheck --> viewer : "Normal Size"
warning --> viewer : "Continue"
warning --> userActions : "Cancel"

viewer --> dialog
dialog --> formatOptions
formatOptions --> qscintilla
formatOptions --> beautifulsoup
formatOptions --> basic

qscintilla --> display
beautifulsoup --> display
basic --> display
display --> saveExport

' Navigation flow
addressInput --> parseURL
parseURL --> loadPage
loadPage --> loadEvent

browserNav --> webview : "Back/Forward/Reload"
webview --> loadEvent

' Tab management flow
tabOps --> newTab
tabOps --> closeTab
tabOps --> switchTab

newTab --> tab
closeTab --> updateUI
switchTab --> updateUI

note right of getSource
  Uses JavaScript to extract
  document.documentElement.outerHTML
end note

note right of sizeCheck
  Checks for files > 5MB
  to detect potential obfuscation
end note

note right of formatOptions
  Multiple formatting options:
  1. QScintilla (syntax highlighting)
  2. BeautifulSoup (pretty print)
  3. Basic (manual formatting)
end note

@enduml

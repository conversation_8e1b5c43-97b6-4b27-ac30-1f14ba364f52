#!/usr/bin/env python3
# -*- coding:utf-8 -*-

################################################################################
# File:    d:\Code\Python\alan\2nd\mybro.py
# Project: d:\Code\Python\alan\2nd
# Created Date: Sunday, June 22nd 2025, 08:13:56
# Author: Jeremy
# ------------------------------------------------------------------------------
# Last Modified: Sunday, June 22nd 2025, 12:06:50
# Modified By: Jeremy
# ------------------------------------------------------------------------------
# Copyright (c)   2025 None
# ---------------- ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ----------------
# HISTORY:
# Date                	By             	Comments
# --------------------	---------------	---------------------------------------
################################################################################

import os
import sys
import tkinter as tk
from tkinter import ttk, messagebox
from loguru import logger
import webbrowser  # Fallback for when pywebview isn't compatible

# Configure logging
log_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'logs')
os.makedirs(log_dir, exist_ok=True)
log_path = os.path.join(log_dir, 'mybro.log')
logger.add(log_path, rotation="10 MB", level="DEBUG")

# Import webview with proper error handling
USING_WEBVIEW = False
try:
    import webview

    # Get version info safely
    try:
        webview_version = webview.__version__
    except AttributeError:
        try:
            webview_version = webview.version
        except AttributeError:
            webview_version = "unknown"

    # Import config for webview renderer selection
    try:
        from config import configure_webview
        renderer = configure_webview()
        logger.info(f"Using {renderer} renderer for webview")
    except ImportError:
        logger.warning(
            "Config module not found, using default webview renderer")
        renderer = None

    # Test if webview is compatible with current Python version
    try:
        # Don't create a test window yet, just set the flag
        USING_WEBVIEW = True
        logger.info(f"Using pywebview {webview_version}")
    except Exception as e:
        logger.error(f"Pywebview compatibility issue: {e}")
        logger.info("Falling back to system browser")
except ImportError:
    logger.error(
        "PyWebview package not installed. Falling back to system browser.")


class SimpleBrowser:
    def __init__(self, root):
        self.root = root
        self.root.title("Simple Browser")
        self.root.geometry("1200x800")

        # Initialize variables first
        self.browser = None
        self.current_url = "https://www.google.com"
        self.status_var = tk.StringVar(value="Ready")
        self.url_var = tk.StringVar(value=self.current_url)

        # Create main container
        self.main_frame = ttk.Frame(root)
        self.main_frame.pack(fill=tk.BOTH, expand=True)

        # Create browser frame
        self.browser_frame = ttk.Frame(self.main_frame)
        self.browser_frame.pack(fill=tk.BOTH, expand=True)

        # Add a message frame for fallback mode
        self.message_frame = ttk.Frame(self.browser_frame)
        self.message_frame.pack(fill=tk.BOTH, expand=True)

        # Create navigation bar
        self.create_navigation_bar()

        # Initialize browser
        self.initialize_browser()

        # Set up keyboard shortcuts
        self.setup_keyboard_shortcuts()

        # Handle window close
        self.root.protocol("WM_DELETE_WINDOW", self.on_close)

    def create_navigation_bar(self):
        """Create the navigation bar with controls"""
        nav_frame = ttk.Frame(self.main_frame)
        nav_frame.pack(fill=tk.X, side=tk.TOP, padx=5, pady=5)

        # Back button
        self.back_btn = ttk.Button(
            nav_frame, text="←", width=3, command=self.go_back)
        self.back_btn.pack(side=tk.LEFT, padx=2)

        # Forward button
        self.forward_btn = ttk.Button(
            nav_frame, text="→", width=3, command=self.go_forward)
        self.forward_btn.pack(side=tk.LEFT, padx=2)

        # Refresh button
        self.refresh_btn = ttk.Button(
            nav_frame, text="↻", width=3, command=self.refresh)
        self.refresh_btn.pack(side=tk.LEFT, padx=2)

        # Home button
        self.home_btn = ttk.Button(
            nav_frame, text="🏠", width=3, command=self.go_home)
        self.home_btn.pack(side=tk.LEFT, padx=2)

        # URL entry
        self.url_entry = ttk.Entry(nav_frame, textvariable=self.url_var)
        self.url_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)
        self.url_entry.bind("<Return>", self.navigate)

        # Go button
        self.go_btn = ttk.Button(nav_frame, text="Go",
                                 width=5, command=self.navigate)
        self.go_btn.pack(side=tk.LEFT, padx=2)

        # Status label
        self.status_label = ttk.Label(
            self.main_frame, textvariable=self.status_var, anchor=tk.W)
        self.status_label.pack(side=tk.BOTTOM, fill=tk.X, padx=5, pady=2)

        # Display fallback message if needed
        if not USING_WEBVIEW:
            message_text = "PyWebView is not compatible with your Python version.\n\n" + \
                "URLs will open in your system's default browser.\n\n" + \
                "Consider downgrading to Python 3.10 or 3.11 for embedded browser support."

            message_label = ttk.Label(
                self.message_frame,
                text=message_text,
                font=("Arial", 14),
                justify=tk.CENTER
            )
            message_label.pack(expand=True)

    def initialize_browser(self):
        """Initialize the web browser component"""
        if not USING_WEBVIEW:
            self.status_var.set("Using system browser (fallback mode)")
            return

        try:
            # Create a temporary window that will be replaced by the embedded browser
            temp_window = ttk.Frame(self.browser_frame)
            temp_window.pack(fill=tk.BOTH, expand=True)

            # Create a label to show loading status
            loading_label = ttk.Label(
                temp_window, text="Loading browser...", font=("Arial", 14))
            loading_label.pack(expand=True)

            # Update the UI to show we're loading
            self.root.update()

            # Set WebView2 as the renderer if available
            import sys
            if sys.platform == 'win32':
                os.environ['PYWEBVIEW_GUI'] = 'edgechromium'
                logger.info("Setting WebView2 (edgechromium) as the renderer")

            # Create the webview window with explicit renderer
            self.browser = webview.create_window(
                'Browser',
                self.current_url,
                hidden=True
            )

            # Define callbacks for browser events
            def on_loaded():
                self.current_url = self.browser.get_current_url()
                self.url_var.set(self.current_url)
                self.status_var.set(f"Loaded: {self.current_url}")

            def on_title_change(title):
                self.root.title(f"Simple Browser - {title}")

            # Start the webview in a separate thread
            webview.start(
                func=lambda: self.setup_browser_callbacks(
                    on_loaded, on_title_change),
                gui='edgechromium'  # Explicitly use WebView2
            )

            logger.info("Browser initialized")

        except Exception as e:
            logger.error(f"Error initializing browser: {e}")
            self.status_var.set(f"Error: {str(e)}")

            # Fall back to system browser
            global USING_WEBVIEW
            USING_WEBVIEW = False

            self.status_var.set("Using system browser (fallback mode)")

            # Show message in browser frame
            message_text = f"Error initializing embedded browser:\n{str(e)}\n\n" + \
                "URLs will open in your system's default browser."

            # Clear existing widgets in message frame
            for widget in self.message_frame.winfo_children():
                widget.destroy()

            message_label = ttk.Label(
                self.message_frame,
                text=message_text,
                font=("Arial", 14),
                justify=tk.CENTER
            )
            message_label.pack(expand=True)

    def setup_browser_callbacks(self, on_loaded, on_title_change):
        """Set up callbacks for browser events"""
        if not USING_WEBVIEW:
            return

        # This function runs in the webview thread
        try:
            # Set up event handlers
            self.browser.loaded += on_loaded

            # Show the browser window
            self.browser.show()

            # Inject JavaScript to monitor title changes
            js_code = """
            (function() {
                var originalTitle = document.title;
                
                // Function to check for title changes
                function checkTitle() {
                    if (document.title !== originalTitle) {
                        originalTitle = document.title;
                        window.pywebview.api.on_title_change(originalTitle);
                    }
                    setTimeout(checkTitle, 500);
                }
                
                // Start monitoring
                checkTitle();
            })();
            """
            self.browser.evaluate_js(js_code)

        except Exception as e:
            logger.error(f"Error in setup_browser_callbacks: {e}")

    def navigate(self, event=None):
        """Navigate to the URL in the entry field"""
        url = self.url_var.get()

        # Add http:// if not present
        if not url.startswith(('http://', 'https://')):
            url = 'https://' + url
            self.url_var.set(url)

        if USING_WEBVIEW and self.browser:
            self.status_var.set(f"Loading: {url}")
            self.browser.load_url(url)
            logger.info(f"Navigating to: {url}")
        else:
            # Fallback to system browser
            self.status_var.set(f"Opening in system browser: {url}")
            webbrowser.open(url)
            logger.info(f"Opening in system browser: {url}")

    def go_back(self):
        """Navigate back in history"""
        if USING_WEBVIEW and self.browser:
            self.browser.evaluate_js('history.back()')
            self.status_var.set("Going back...")
            logger.info("Navigating back")
        else:
            self.status_var.set(
                "History navigation not available in fallback mode")

    def go_forward(self):
        """Navigate forward in history"""
        if USING_WEBVIEW and self.browser:
            self.browser.evaluate_js('history.forward()')
            self.status_var.set("Going forward...")
            logger.info("Navigating forward")
        else:
            self.status_var.set(
                "History navigation not available in fallback mode")

    def refresh(self):
        """Refresh the current page"""
        if USING_WEBVIEW and self.browser:
            self.browser.evaluate_js('location.reload()')
            self.status_var.set("Refreshing page...")
            logger.info("Refreshing page")
        else:
            # Re-open the current URL in system browser
            self.navigate()

    def go_home(self):
        """Navigate to home page (Google)"""
        home_url = "https://www.google.com"
        self.url_var.set(home_url)

        if USING_WEBVIEW and self.browser:
            self.browser.load_url(home_url)
            self.status_var.set(f"Loading home: {home_url}")
        else:
            # Fallback to system browser
            webbrowser.open(home_url)
            self.status_var.set(f"Opening home in system browser: {home_url}")

        logger.info(f"Navigating to home: {home_url}")

    def setup_keyboard_shortcuts(self):
        """Set up keyboard shortcuts for browser navigation"""
        self.root.bind("<F5>", lambda e: self.refresh())
        self.root.bind("<Control-r>", lambda e: self.refresh())
        self.root.bind("<Alt-Left>", lambda e: self.go_back())
        self.root.bind("<Alt-Right>", lambda e: self.go_forward())
        self.root.bind("<Alt-Home>", lambda e: self.go_home())
        self.root.bind("<Control-l>", lambda e: self.url_entry.focus_set())

    def on_close(self):
        """Handle window close event"""
        try:
            logger.info("Closing browser")
            if USING_WEBVIEW and self.browser:
                self.browser.destroy()
            self.root.destroy()
        except Exception as e:
            logger.error(f"Error closing browser: {e}")
            self.root.destroy()


def main():
    """Main entry point for the application"""
    logger.info("Application starting")

    root = tk.Tk()
    app = SimpleBrowser(root)

    # Start the main loop
    root.mainloop()

    logger.info("Application exited")


if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding:utf-8 -*-

################################################################################
# File:    d:\Code\Python\alan\2nd\config.py
# Project: d:\Code\Python\alan\2nd
# Created Date: Saturday, June 21st 2025, 08:06:42
# Author: Jeremy
# ------------------------------------------------------------------------------
# Last Modified: Sunday, June 22nd 2025, 12:04:22
# Modified By: Jeremy
# ------------------------------------------------------------------------------
# Copyright (c)   2025 None
# ---------------- ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ----------------
# HISTORY:
# Date                	By             	Comments
# --------------------	---------------	---------------------------------------
################################################################################

import os
import importlib.util

# Fallback order: WebView2 -> CEF -> Qt -> MSHTML (last resort)
# You can change this order by rearranging the list
WEBVIEW_RENDERERS = ['edgechromium', 'cef', 'qt', 'mshtml']


def configure_webview():
    """Configure webview with appropriate fallbacks"""
    # Check if webview is installed
    if importlib.util.find_spec("webview") is None:
        print("PyWebView not installed")
        return None

    # Import webview
    import webview

    # Get webview version
    try:
        webview_version = webview.__version__
    except AttributeError:
        try:
            webview_version = webview.version
        except AttributeError:
            webview_version = "unknown"

    print(f"Using pywebview version: {webview_version}")

    # Check Python version compatibility
    import sys
    python_version = sys.version
    print(f"Python version: {python_version}")

    # Force edgechromium for Windows 10 with WebView2 installed
    if sys.platform == 'win32':
        try:
            # Check if WebView2 is installed via registry
            import winreg
            registry_path = r"SOFTWARE\WOW6432Node\Microsoft\EdgeUpdate\Clients\{F3017226-FE2A-4295-8BDF-00C3A9A7E4C5}"
            try:
                with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, registry_path) as key:
                    version = winreg.QueryValueEx(key, "pv")[0]
                    print(f"WebView2 found in registry, version: {version}")
                    os.environ['PYWEBVIEW_GUI'] = 'edgechromium'
                    return 'edgechromium'
            except WindowsError:
                print("WebView2 not found in registry")
        except ImportError:
            print("Unable to check registry for WebView2")

    # Try to use the first available renderer
    for renderer in WEBVIEW_RENDERERS:
        os.environ['PYWEBVIEW_GUI'] = renderer
        try:
            # Just check if the renderer is available without creating a window
            if hasattr(webview, 'platforms'):
                if renderer in webview.platforms:
                    print(f"Using {renderer} renderer")
                    return renderer
            else:
                # For older versions, try to use the renderer
                print(f"Trying {renderer} renderer")
                return renderer
        except Exception as e:
            print(f"Failed to initialize {renderer}: {e}")
            continue

    # If all renderers fail, use whatever is available
    os.environ.pop('PYWEBVIEW_GUI', None)
    return None

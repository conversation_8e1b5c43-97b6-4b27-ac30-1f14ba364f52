#!/usr/bin/env python3
# -*- coding:utf-8 -*-

################################################################################
# File:    d:\Code\Python\alan\2nd\mybro.py
# Project: d:\Code\Python\alan\2nd
# Created Date: Thursday, June 12th 2025, 18:58:30
# Author: Jeremy
# ------------------------------------------------------------------------------
# Last Modified: Sunday, June 22nd 2025, 07:41:37
# Modified By: Jeremy
# ------------------------------------------------------------------------------
# Copyright (c)   2025 None
# ---------------- ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ----------------
# HISTORY:
# Date                	By             	Comments
# --------------------	---------------	---------------------------------------
################################################################################
import ctypes
import uuid
import json
import io
import base64
from pathlib import Path
import pytesseract
from PIL import Image, ImageTk
import ttkthemes
from tkinter import ttk, messagebox, filedialog
import tkinter as tk
from loguru import logger
from urllib.parse import urlparse
import time
import threading
import re
import datetime
import zipfile
import shutil
import sys
import os

# Import config for webview fallbacks
try:
    from config import configure_webview
    renderer = configure_webview()
    logger.info(f"Using {renderer} renderer for webview")
except ImportError:
    logger.warning("Config module not found, using default webview renderer")
    renderer = None

# Import webview with proper error handling
try:
    import webview
    USING_WEBVIEW = True

    # Get version info safely
    try:
        webview_version = webview.__version__
    except AttributeError:
        try:
            webview_version = webview.version
        except AttributeError:
            webview_version = "unknown"

    try:
        webview_platform = webview.PLATFORM
    except AttributeError:
        try:
            webview_platform = webview.platform
        except AttributeError:
            webview_platform = "unknown"

    # Determine if we're using a newer or older version
    WEBVIEW_VERSION_4_PLUS = False
    try:
        major_version = int(webview_version.split('.')[0])
        WEBVIEW_VERSION_4_PLUS = major_version >= 4
    except (ValueError, AttributeError, IndexError):
        # If we can't determine version, assume older version
        WEBVIEW_VERSION_4_PLUS = False

    logger.info(
        f"Using pywebview {webview_version} with {webview_platform} renderer")
except ImportError:
    logger.error(
        "PyWebview package not installed. Please install it using: pip install pywebview")
    USING_WEBVIEW = False
    sys.exit(1)
# For debugging
import debugpy
# Only enable debugging in development mode
if os.environ.get('MYBRO_DEBUG', '0') == '1':
    debugpy.debug_this_thread()
    logger.info("Debugpy enabled - waiting for debugger to attach")


class MyBro:
    def __init__(self, root):
        self.root = root
        self.base_dir = os.path.dirname(os.path.abspath(__file__))

        # Initialize environment
        self.initenv()

        # Variables
        self.current_url = ""
        self.page_loaded = False
        self.analayer_enabled = False
        self.rectangles = []
        self.selected_rectangle = None
        self.current_color = "red"
        self.template_counter = 1
        self.drawing = False
        self.drawing_mode = True  # Default to drawing mode (not passthrough)
        self.start_x = 0
        self.start_y = 0
        self.browser = None
        self.browser_initialized = False
        self.aux_line_h_id = None
        self.aux_line_v_id = None
        self.aux_line_color = "red"
        self.aux_lines_enabled = True
        self.passthrough_enabled = False

        # Setup UI
        self.setup_ui()

        # Setup keyboard shortcuts
        self.setup_keyboard_shortcuts()

        # Bind window close event
        self.root.protocol("WM_DELETE_WINDOW", self.on_close)

    def initenv(self):
        """Initialize environment directories"""
        # Create necessary directories
        dirs = ['logs', 'snap', 'temp']
        for dir_name in dirs:
            dir_path = os.path.join(self.base_dir, dir_name)
            os.makedirs(dir_path, exist_ok=True)
        logger.info("Environment initialized")

    def _update_url_entry(self):
        """Update the URL entry with the current URL"""
        if hasattr(self, 'url_entry'):
            self.url_entry.delete(0, tk.END)
            self.url_entry.insert(0, self.current_url)

    def navigate(self, event=None):
        """Navigate to the URL in the entry field"""
        url = self.url_entry.get()
        self.navigate_to(url)

    def navigate_to(self, url):
        """Navigate to the specified URL"""
        # Add http:// if not present
        if not url.startswith(('http://', 'https://')):
            url = 'https://' + url

        if USING_WEBVIEW and hasattr(self, 'browser') and self.browser:
            self.browser.load_url(url)
            logger.info(f"Navigating to: {url}")
        else:
            logger.warning("Browser not initialized yet")

    def go_back(self):
        """Navigate back in history"""
        if USING_WEBVIEW and hasattr(self, 'browser') and self.browser:
            self.browser.evaluate_js('history.back()')
            logger.info("Navigating back")

    def go_forward(self):
        """Navigate forward in history"""
        if USING_WEBVIEW and hasattr(self, 'browser') and self.browser:
            self.browser.evaluate_js('history.forward()')
            logger.info("Navigating forward")

    def refresh(self):
        """Refresh the current page"""
        if USING_WEBVIEW and hasattr(self, 'browser') and self.browser:
            self.browser.evaluate_js('location.reload()')
            logger.info("Refreshing page")

    def get_domain_serial(self, folder, domain):
        """Get or create a serial number for the domain"""
        # Create domain tracking file if it doesn't exist
        domain_file = os.path.join(folder, "domains.json")
        os.makedirs(folder, exist_ok=True)

        domains = {}
        if os.path.exists(domain_file):
            try:
                with open(domain_file, "r") as f:
                    domains = json.load(f)
            except json.JSONDecodeError:
                # If file is corrupted, start fresh
                domains = {}

        # Get or create serial for domain
        if domain in domains:
            return domains[domain]
        else:
            # Find the next available serial number
            next_serial = 1
            if domains:
                next_serial = max(domains.values()) + 1

            # Save the new domain serial
            domains[domain] = next_serial
            with open(domain_file, "w") as f:
                json.dump(domains, f, indent=2)

            return next_serial

    def convert_coordinates(self, x, y):
        """Convert canvas coordinates to browser coordinates"""
        # This is a simple implementation - you might need to adjust based on scaling factors
        # or other considerations specific to your application
        if hasattr(self, 'analayer_canvas') and self.analayer_canvas:
            # If there's any scaling or offset between the canvas and browser view,
            # you would account for it here
            return x, y
        return x, y

    def setup_ui(self):
        """Set up the user interface"""
        # Configure the root window
        self.root.title("MyBro Browser")
        self.root.geometry("1200x800")

        # Apply theme
        self.theme = ttkthemes.ThemedStyle(self.root)
        self.theme.set_theme("arc")  # Use a modern theme

        # Create main frame
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Create toolbar
        toolbar = ttk.Frame(main_frame)
        toolbar.pack(fill=tk.X, side=tk.TOP)

        # Navigation buttons
        back_btn = ttk.Button(toolbar, text="←", width=2, command=self.go_back)
        back_btn.pack(side=tk.LEFT, padx=2)

        forward_btn = ttk.Button(
            toolbar, text="→", width=2, command=self.go_forward)
        forward_btn.pack(side=tk.LEFT, padx=2)

        refresh_btn = ttk.Button(
            toolbar, text="↻", width=2, command=self.refresh)
        refresh_btn.pack(side=tk.LEFT, padx=2)

        # URL entry
        self.url_entry = ttk.Entry(toolbar)
        self.url_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)
        self.url_entry.bind("<Return>", self.navigate)

        # WebSnap button
        self.websnap_btn = ttk.Button(
            toolbar, text="WebSnap", command=self.take_websnap)
        self.websnap_btn.pack(side=tk.LEFT, padx=5)
        self.websnap_btn.config(state=tk.DISABLED)  # Disabled until page loads

        # Analysis Layer button
        self.analayer_btn = ttk.Button(
            toolbar, text="Analysis Layer", command=self.toggle_analayer)
        self.analayer_btn.pack(side=tk.LEFT, padx=5)

        # Mode Toggle button
        self.mode_btn = ttk.Button(
            toolbar, text="Toggle Mode", command=self.toggle_drawing_mode)
        self.mode_btn.pack(side=tk.LEFT, padx=5)

        # Browser frame
        self.browser_frame = ttk.Frame(main_frame)
        self.browser_frame.pack(fill=tk.BOTH, expand=True)

        # Initialize browser after UI is set up
        self.root.after(10, self.initialize_browser)

        logger.info("UI setup complete")

    def _start_webview(self):
        """Start the webview browser in a separate thread"""
        try:
            # Start webview with appropriate parameters
            if hasattr(webview, 'start'):
                # Just call start() without any parameters to be safe
                webview.start()
            else:
                logger.error(
                    "This version of pywebview doesn't have a start method")
        except Exception as e:
            logger.error(f"Failed to start webview: {e}")
            if hasattr(self, 'root') and self.root:
                self.root.after(0, lambda: messagebox.showerror(
                    "Error", f"Failed to start webview: {e}"))

    def _on_page_loaded(self):
        """Called when a page is loaded in webview"""
        if hasattr(self, 'browser') and self.browser:
            self.current_url = self.browser.get_current_url()
            self.page_loaded = True

            # Update UI in main thread
            if hasattr(self, 'root') and self.root:
                self.root.after(0, self._update_url_entry)
                self.root.after(
                    0, lambda: self.websnap_btn.config(state=tk.NORMAL))

            logger.info(f"Navigation completed: {self.current_url}")

    def _on_browser_shown(self):
        """Called when the browser window is shown"""
        logger.info("Browser window shown")

    def take_websnap(self):
        """Take a snapshot of the entire webpage"""
        if not self.page_loaded or not self.browser_initialized:
            logger.warning(
                "Cannot take websnap: page not fully loaded or browser not initialized")
            return None

        # Get domain from URL
        parsed_url = urlparse(self.current_url)
        domain = parsed_url.netloc

        # Setup paths
        current_date = datetime.datetime.now().strftime("%Y%m%d")
        snap_folder = os.path.join(self.base_dir, 'snap', current_date)

        # Get or create file serial number for this domain
        file_sn = self.get_domain_serial(snap_folder, domain)

        # Create site folder if it doesn't exist
        site_folder = os.path.join(snap_folder, domain)
        os.makedirs(site_folder, exist_ok=True)

        # Create filename
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"snap_{file_sn:04d}_{timestamp}.png"
        snap_path = os.path.join(site_folder, filename)

        # Update or create site ml.list
        site_ml_path = os.path.join(site_folder, "ml.list")
        if not os.path.exists(site_ml_path):
            with open(site_ml_path, "w") as f:
                f.write("# Serial Number - Full URL\n")

        # Add URL to site ml.list if not already there
        url_exists = False
        if os.path.exists(site_ml_path):
            with open(site_ml_path, "r") as f:
                for line in f:
                    if line.strip() and not line.startswith("#"):
                        if self.current_url in line:
                            url_exists = True
                            break

        if not url_exists:
            with open(site_ml_path, "a") as f:
                f.write(f"{file_sn:04d} - {self.current_url}\n")

        try:
            # Take screenshot using WebView
            if USING_WEBVIEW and hasattr(self, 'browser') and self.browser:
                try:
                    # Try direct screenshot method first
                    if hasattr(self.browser, 'get_screenshot'):
                        png_data = self.browser.get_screenshot()
                        with open(snap_path, 'wb') as f:
                            f.write(png_data)
                        logger.info(f"Screenshot saved to {snap_path}")
                        return snap_path
                    # Fallback to JS-based screenshot
                    else:
                        # Use html2canvas for screenshot
                        js_code = """
                        (function() {
                            // Load html2canvas if not already loaded
                            if (typeof html2canvas === 'undefined') {
                                var script = document.createElement('script');
                                script.src = 'https://html2canvas.hertzen.com/dist/html2canvas.min.js';
                                script.onload = function() {
                                    captureScreenshot();
                                };
                                document.head.appendChild(script);
                            } else {
                                captureScreenshot();
                            }

                            function captureScreenshot() {
                                html2canvas(document.body, {
                                    allowTaint: true,
                                    useCORS: true,
                                    scrollX: 0,
                                    scrollY: 0,
                                    windowWidth: document.documentElement.offsetWidth,
                                    windowHeight: document.documentElement.offsetHeight
                                }).then(function(canvas) {
                                    return canvas.toDataURL('image/png');
                                }).then(function(dataUrl) {
                                    window.pywebview.state.setScreenshot(dataUrl);
                                });
                            }
                        })();
                        """
                        self.browser.evaluate_js(js_code)
                        # This will be handled by the setScreenshot method
                        logger.info("Using JavaScript-based screenshot method")
                        return None  # Will be handled asynchronously
                except Exception as e:
                    logger.error(f"Error taking screenshot: {e}")
                    messagebox.showerror(
                        "Error", f"Failed to take screenshot: {e}")
                    return None
            else:
                logger.error("WebView not available for taking screenshot")
                return None
        except Exception as e:
            logger.error(f"Error in take_websnap: {e}")
            return None

    def setScreenshot(self, data_url):
        """Receive screenshot data from JavaScript"""
        try:
            # Extract the base64 data from the data URL
            header, encoded = data_url.split(",", 1)
            data = base64.b64decode(encoded)

            # Get domain from URL
            parsed_url = urlparse(self.current_url)
            domain = parsed_url.netloc

            # Setup paths
            current_date = datetime.datetime.now().strftime("%Y%m%d")
            snap_folder = os.path.join(self.base_dir, 'snap', current_date)

            # Get or create file serial number for this domain
            file_sn = self.get_domain_serial(snap_folder, domain)

            # Create site folder if it doesn't exist
            site_folder = os.path.join(snap_folder, domain)
            os.makedirs(site_folder, exist_ok=True)

            # Determine snap serial number
            snap_files = [f for f in os.listdir(site_folder)
                          if f.startswith(f"snap_{file_sn:04d}_")]
            snap_sn = len(snap_files) + 1

            # Save the screenshot
            snap_filename = f"snap_{file_sn:04d}_{snap_sn:04d}.png"
            snap_path = os.path.join(site_folder, snap_filename)

            with open(snap_path, 'wb') as f:
                f.write(data)

            logger.info(f"Screenshot saved to: {snap_path}")
            messagebox.showinfo(
                "WebSnap", f"Screenshot saved to:\n{snap_path}")
            return snap_path
        except Exception as e:
            logger.error(f"Error saving screenshot: {e}")
            messagebox.showerror("Error", f"Failed to save screenshot: {e}")
            return None

    def set_color(self, color):
        """Set the current drawing color"""
        self.current_color = color
        logger.debug(f"Color set to: {color}")

    def toggle_drawing_mode(self):
        """Toggle between drawing and passthrough modes"""
        self.drawing_mode = not self.drawing_mode

        if self.drawing_mode:
            # Switch to drawing mode
            if hasattr(self, 'color_frame'):
                self.color_frame.pack(
                    fill=tk.X, side=tk.BOTTOM, padx=5, pady=5)
            logger.info("Switched to drawing mode")
            messagebox.showinfo("Mode Change", "Drawing mode enabled")
        else:
            # Switch to passthrough mode
            if hasattr(self, 'color_frame'):
                self.color_frame.pack_forget()
            logger.info("Switched to passthrough mode")
            messagebox.showinfo("Mode Change", "Passthrough mode enabled")

    def setup_context_menu(self):
        """Set up the right-click context menu"""
        self.context_menu = tk.Menu(self.root, tearoff=0)
        self.context_menu.add_command(
            label="Delete Rectangle", command=self.delete_selected_rectangle)
        self.context_menu.add_command(
            label="Clear All", command=self.clear_all_rectangles)
        self.context_menu.add_separator()
        self.context_menu.add_command(
            label="Toggle Drawing Mode", command=self.toggle_drawing_mode)

        # Bind right-click to show context menu
        if hasattr(self, 'analayer_canvas'):
            self.analayer_canvas.bind("<Button-3>", self.show_context_menu)

    def show_context_menu(self, event):
        """Show the context menu at the current mouse position"""
        if self.analayer_enabled:
            self.context_menu.post(event.x_root, event.y_root)

    def delete_selected_rectangle(self):
        """Delete the currently selected rectangle"""
        if self.selected_rectangle:
            # Delete from canvas
            self.analayer_canvas.delete(self.selected_rectangle['id'])

            # Remove from list
            self.rectangles.remove(self.selected_rectangle)

            # Clear selection
            self.selected_rectangle = None

            logger.info("Rectangle deleted")

    def clear_all_rectangles(self):
        """Clear all rectangles from the canvas"""
        if hasattr(self, 'analayer_canvas'):
            # Delete all rectangles from canvas
            for rect in self.rectangles:
                self.analayer_canvas.delete(rect['id'])

            # Clear list
            self.rectangles = []

            # Clear selection
            self.selected_rectangle = None

            logger.info("All rectangles cleared")

    def clear_selection(self):
        """Clear the current rectangle selection"""
        self.selected_rectangle = None
        logger.debug("Selection cleared")

    def setup_keyboard_shortcuts(self):
        """Set up keyboard shortcuts"""
        # Navigation shortcuts
        self.root.bind("<Control-r>", lambda e: self.refresh())
        self.root.bind("<F5>", lambda e: self.refresh())
        self.root.bind("<Alt-Left>", lambda e: self.go_back())
        self.root.bind("<Alt-Right>", lambda e: self.go_forward())

        # Analysis layer shortcuts
        self.root.bind("<Control-a>", lambda e: self.toggle_analayer())
        self.root.bind("<Control-d>", lambda e: self.toggle_drawing_mode())
        self.root.bind("<Control-s>", lambda e: self.take_websnap())

        # Rectangle management
        self.root.bind("<Delete>", lambda e: self.delete_selected_rectangle())
        self.root.bind("<Escape>", lambda e: self.clear_selection())

        logger.info("Keyboard shortcuts configured")

    def on_release(self, event):
        """Handle mouse release event"""
        if not self.analayer_enabled or not self.drawing or not self.drawing_mode:
            return

        # Stop drawing
        self.drawing = False

        # Remove auxiliary lines
        if self.aux_lines_enabled:
            if self.aux_line_h_id:
                self._safe_canvas_operation('delete', self.aux_line_h_id)
                self.aux_line_h_id = None
            if self.aux_line_v_id:
                self._safe_canvas_operation('delete', self.aux_line_v_id)
                self.aux_line_v_id = None

        # Create rectangle
        x1, y1 = self.start_x, self.start_y
        x2, y2 = event.x, event.y

        # Ensure x1,y1 is the top-left and x2,y2 is the bottom-right
        if x1 > x2:
            x1, x2 = x2, x1
        if y1 > y2:
            y1, y2 = y2, y1

        # Only create if it has some size
        if abs(x2 - x1) > 5 and abs(y2 - y1) > 5:
            # Create the rectangle on the canvas
            rect_id = self._safe_canvas_operation('create_rectangle',
                                                  x1, y1, x2, y2,
                                                  outline=self.current_color,
                                                  width=2,
                                                  tags=f"rect_{len(self.rectangles)}")

            if rect_id:
                # Store the rectangle info
                rect_info = {
                    'id': rect_id,
                    'coords': (x1, y1, x2, y2),
                    'color': self.current_color,
                    'tag': f"rect_{len(self.rectangles)}"
                }

                # Add to our list of rectangles
                self.rectangles.append(rect_info)
                logger.info(f"Created rectangle: {rect_info}")

    def on_press(self, event):
        """Handle mouse press event"""
        if not self.analayer_enabled or not self.drawing_mode:
            return

        self.start_x = event.x
        self.start_y = event.y
        self.drawing = True
        logger.debug(f"Started drawing at ({event.x}, {event.y})")

    def on_drag(self, event):
        """Handle mouse drag event"""
        if not self.analayer_enabled or not self.drawing or not self.drawing_mode:
            return

        # Draw auxiliary lines if enabled
        if self.aux_lines_enabled:
            # Remove previous auxiliary lines if they exist
            if self.aux_line_h_id:
                try:
                    self.analayer_canvas.delete(self.aux_line_h_id)
                except Exception as e:
                    logger.error(f"Error deleting horizontal line: {e}")

            if self.aux_line_v_id:
                try:
                    self.analayer_canvas.delete(self.aux_line_v_id)
                except Exception as e:
                    logger.error(f"Error deleting vertical line: {e}")

            # Draw new auxiliary lines
            try:
                self.aux_line_h_id = self.analayer_canvas.create_line(
                    0, event.y, self.analayer_canvas.winfo_width(), event.y,
                    fill=self.aux_line_color, dash=(4, 4))

                self.aux_line_v_id = self.analayer_canvas.create_line(
                    event.x, 0, event.x, self.analayer_canvas.winfo_height(),
                    fill=self.aux_line_color, dash=(4, 4))
            except Exception as e:
                logger.error(f"Error creating auxiliary lines: {e}")

    def on_close(self):
        """Handle window close event"""
        try:
            # Clean up resources
            if USING_WEBVIEW and hasattr(self, 'browser') and self.browser:
                # Close the browser window
                self.browser.destroy()

            # Wait for browser thread to finish if it exists
            if hasattr(self, 'browser_thread') and self.browser_thread and self.browser_thread.is_alive():
                self.browser_thread.join(timeout=1.0)

            logger.info("Application closing")
            self.root.destroy()
        except Exception as e:
            logger.error(f"Error during application close: {e}")
            # Force quit if we can't close gracefully
            self.root.destroy()

    def get_element_info(self, x, y):
        """Get information about the element at the given coordinates"""
        if USING_WEBVIEW and hasattr(self, 'browser') and self.browser:
            # Convert canvas coordinates to browser coordinates
            browser_x, browser_y = self.convert_coordinates(x, y)

            # JavaScript to get element info
            js_code = f"""
            (function() {{
                var element = document.elementFromPoint({browser_x}, {browser_y});
                if (element) {{
                    return {{
                        tagName: element.tagName,
                        id: element.id,
                        className: element.className,
                        text: element.innerText ? element.innerText.substring(0, 100) : '',
                        attributes: Array.from(element.attributes).map(attr => [attr.name, attr.value])
                    }};
                }}
                return null;
            }})();
            """

            # Execute JavaScript and get result
            result = self.browser.evaluate_js(js_code)
            return result
        return None

    def extract_text_from_area(self, x1, y1, x2, y2):
        """Extract text from the specified area using OCR"""
        if not self.page_loaded or not self.browser_initialized:
            logger.warning(
                "Cannot extract text: page not fully loaded or browser not initialized")
            return None

        try:
            # Take a screenshot first
            snap_path = self.take_websnap()
            if not snap_path or not os.path.exists(snap_path):
                logger.error("Failed to take screenshot for OCR")
                return None

            # Open the image
            image = Image.open(snap_path)

            # Convert canvas coordinates to image coordinates (may need adjustment)
            # This assumes 1:1 mapping between canvas and image

            # Crop the image to the selected area
            cropped = image.crop((x1, y1, x2, y2))

            # Use pytesseract to extract text
            text = pytesseract.image_to_string(cropped)

            logger.info(
                f"Extracted text from area ({x1},{y1},{x2},{y2}): {text[:50]}...")
            return text
        except Exception as e:
            logger.error(f"Error extracting text: {e}")
            return None

    def toggle_analayer(self):
        """Toggle the analysis layer on/off"""
        if not hasattr(self, 'analayer_canvas') or self.analayer_canvas is None:
            logger.error("Analysis layer not initialized")
            return

        if self.analayer_enabled:
            # Disable analysis layer - use pack_forget instead of lower()
            try:
                self.analayer_canvas.pack_forget()
                self.analayer_enabled = False
                logger.info("Analysis layer disabled")
            except Exception as e:
                logger.error(f"Error disabling analysis layer: {e}")
        else:
            # Enable analysis layer - repack the canvas
            try:
                self.analayer_canvas.pack(fill=tk.BOTH, expand=True)
                self.analayer_enabled = True
                logger.info("Analysis layer enabled")
            except Exception as e:
                logger.error(f"Error enabling analysis layer: {e}")

    def _start_webview(self):
        """Start the webview browser in a separate thread"""
        try:
            # Start webview with appropriate parameters
            if hasattr(webview, 'start'):
                # Just call start() without any parameters to be safe
                webview.start()
            else:
                logger.error(
                    "This version of pywebview doesn't have a start method")
        except Exception as e:
            error_msg = str(e)
            logger.error(f"Failed to start webview: {error_msg}")
            if hasattr(self, 'root') and self.root:
                # Use a local variable to avoid the free variable error
                def show_error():
                    messagebox.showerror(
                        "Error", f"Failed to start webview: {error_msg}")
                self.root.after(0, show_error)

    def initialize_browser(self):
        """Initialize the browser component"""
        # Create a frame for the browser
        self.browser_container = ttk.Frame(self.browser_frame)
        self.browser_container.pack(fill=tk.BOTH, expand=True)

        # Initialize analysis layer first
        self.setup_analayer()

        if USING_WEBVIEW:
            try:
                # Create webview window
                def create_webview_window():
                    self.browser = webview.create_window(
                        'MyBro Browser',
                        'https://www.google.com',
                        width=800,
                        height=600,
                        resizable=True,
                        frameless=False,
                        easy_drag=False,
                        js_api=self  # Expose Python functions to JavaScript
                    )

                    # Start webview in the main thread
                    webview.start()

                # Schedule webview creation for later to avoid threading issues
                self.root.after(100, lambda: threading.Thread(
                    target=create_webview_window, daemon=True).start())

                # Update UI state
                self.browser_initialized = True
                self.current_url = "https://www.google.com"
                self._update_url_entry()

                logger.info("Browser initialized successfully")
            except Exception as e:
                logger.error(f"Failed to initialize browser: {e}")
                messagebox.showerror(
                    "Error", f"Failed to initialize browser: {e}")
        else:
            logger.error("WebView not available")
            messagebox.showerror(
                "Error", "WebView not available. Please install pywebview.")

    def _safe_canvas_operation(self, method_name, *args, **kwargs):
        """Safely call a canvas method with proper error handling"""
        if hasattr(self, 'analayer_canvas') and self.analayer_canvas:
            try:
                method = getattr(self.analayer_canvas, method_name)
                if method:
                    if args or kwargs:
                        return method(*args, **kwargs)
                    else:
                        return method()
            except Exception as e:
                logger.error(f"Error calling canvas method {method_name}: {e}")
        return None

    def setup_analayer(self):
        """Set up the analysis layer for annotations"""
        try:
            # Create a canvas that overlays the browser
            self.analayer_canvas = tk.Canvas(
                self.browser_frame,
                highlightthickness=0
            )

            # Position the canvas
            self.analayer_canvas.pack(fill=tk.BOTH, expand=True)

            # Bind mouse events for drawing
            self.analayer_canvas.bind("<Button-1>", self.on_press)

            # Only bind if the method exists
            if hasattr(self, 'on_drag'):
                self.analayer_canvas.bind("<B1-Motion>", self.on_drag)
            else:
                logger.warning("on_drag method not defined, skipping binding")

            self.analayer_canvas.bind("<ButtonRelease-1>", self.on_release)

            # Initially hide the canvas - don't use lower() which is causing issues
            self.analayer_canvas.pack_forget()
            self.analayer_enabled = False

            # Setup context menu and color panel if those methods exist
            if hasattr(self, 'setup_context_menu'):
                self.setup_context_menu()

            if hasattr(self, 'setup_color_panel'):
                self.setup_color_panel()

            logger.info("Analysis layer initialized")
        except Exception as e:
            logger.error(f"Error setting up analysis layer: {e}")
            # Make sure we don't leave the attribute unset
            self.analayer_canvas = None
            self.analayer_enabled = False

    def on_press_passthrough(self, event):
        """Handle mouse press event in passthrough mode"""
        if self.passthrough_enabled:
            # Pass the event to the underlying browser
            pass
        else:
            self.on_press(event)

    def on_drag_passthrough(self, event):
        """Handle mouse drag event in passthrough mode"""
        if self.passthrough_enabled:
            # Pass the event to the underlying browser
            pass
        else:
            self.on_drag(event)

    def on_release_passthrough(self, event):
        """Handle mouse release event in passthrough mode"""
        if self.passthrough_enabled:
            # Pass the event to the underlying browser
            pass
        else:
            self.on_release(event)


def main():
    """Main entry point for the application"""
    # Configure logging
    log_path = os.path.join(os.path.dirname(
        os.path.abspath(__file__)), 'logs', 'mybro.log')
    logger.add(log_path, rotation="10 MB", level="DEBUG")
    logger.info("Application starting")

    # Create the main window
    root = tk.Tk()
    app = MyBro(root)

    # Start the main loop
    root.mainloop()

    logger.info("Application exited")


if __name__ == "__main__":
    main()

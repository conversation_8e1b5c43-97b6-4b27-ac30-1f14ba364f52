#!/usr/bin/env python3
# -*- coding:utf-8 -*-

################################################################################
# File:    d:\Code\Python\alan\2nd\test_pyside_web.py
# Project: d:\Code\Python\alan\2nd
# Created Date: Sunday, June 22nd 2025, 15:38:27
# Author: Jeremy
# ------------------------------------------------------------------------------
# Last Modified: Sunday, June 22nd 2025, 15:38:47
# Modified By: Jeremy
# ------------------------------------------------------------------------------
# Copyright (c)   2025 None
# ---------------- ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ----------------
# HISTORY: 
# Date                	By             	Comments
# --------------------	---------------	---------------------------------------
################################################################################
import sys
from PySide6 import __version__ as pyside_version
from PySide6.QtCore import __version__ as qt_version
from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton
from PySide6.QtCore import Qt, QUrl
from PySide6.QtWebEngineWidgets import QWebEngineView

class SimpleBrowser(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("PySide6 Web Browser Test")
        self.setGeometry(100, 100, 1024, 768)
        
        # Create central widget and layout
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # Create web view
        self.web_view = QWebEngineView()
        self.web_view.load(QUrl("https://www.google.com"))
        layout.addWidget(self.web_view)
        
        # Add a button to show versions
        version_button = QPushButton("Show Versions")
        version_button.clicked.connect(self.show_versions)
        layout.addWidget(version_button)
    
    def show_versions(self):
        print(f"PySide6 version: {pyside_version}")
        print(f"Qt version: {qt_version}")
        print("QtWebEngine is working!")

if __name__ == "__main__":
    app = QApplication(sys.argv)
    browser = SimpleBrowser()
    browser.show()
    sys.exit(app.exec())
    
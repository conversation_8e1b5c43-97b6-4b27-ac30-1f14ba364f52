#!/usr/bin/env python3
# -*- coding:utf-8 -*-

################################################################################
# File:    d:\Code\Python\alan\2nd\test_pyside.py
# Project: d:\Code\Python\alan\2nd
# Created Date: Sunday, June 22nd 2025, 15:30:10
# Author: Jeremy
# ------------------------------------------------------------------------------
# Last Modified: Sunday, June 22nd 2025, 15:33:01
# Modified By: Jeremy
# ------------------------------------------------------------------------------
# Copyright (c)   2025 None
# ---------------- ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ----------------
# HISTORY: 
# Date                	By             	Comments
# --------------------	---------------	---------------------------------------
################################################################################
import sys
from PySide6 import __version__ as pyside_version
from PySide6.QtCore import __version__ as qt_version
from PySide6.QtWidgets import QApplication, QLabel
from PySide6.QtCore import Qt, QTimer

app = QApplication(sys.argv)

# Print versions
print(f"PySide6 version: {pyside_version}")
print(f"Qt version: {qt_version}")

# Create a simple window
label = QLabel("PySide6 is working!")
label.setAlignment(Qt.AlignCenter)
label.resize(400, 200)
label.show()

# Auto-close after 3 seconds
QTimer.singleShot(3000, app.quit)

sys.exit(app.exec())
```plantuml
@startuml
flowchart TD
    A[Application Start] --> B[MyBrowser Main Window]
    B --> C[Setup Window Position]
    C --> D[Setup Toolbar]
    C --> E[Create Tab Widget]
    C --> F[Create Status Bar]
    C --> G[Setup Shortcuts]
    
    D --> D1[Back Button]
    D --> D2[Forward Button]
    D --> D3[Reload Button]
    D --> D4[Home Button]
    D --> D5[Address Bar]
    D --> D6[Source Button]
    D --> D7[User Button]
    D --> D8[System Button]
    
    E --> H[Add First Browser Tab]
    H --> I[BrowserTab Widget]
    
    I --> J[QWebEngineView]
    J --> K[Load Default URL<br/>google.com]
    
    K --> L{Page Load Event}
    L -->|Success| M[Enable Source Button]
    L -->|Failure| N[Keep Source Button Disabled]
    
    M --> O[User Actions]
    N --> O
    
    O --> P{User Action Type}
    
    P -->|Navigate| Q[Address Bar Input]
    Q --> R[Parse URL]
    R --> S[Load New Page]
    S --> L
    
    P -->|View Source| T[Get Page Source]
    T --> U[JavaScript Execution]
    U --> V[Retrieve HTML]
    V --> W{Source Size Check}
    W -->|Large| X[Show Warning Dialog]
    W -->|Normal| Y[Show Source Viewer]
    X -->|Continue| Y
    X -->|Cancel| O
    
    Y --> Z[Source Dialog]
    Z --> AA[Format HTML Options]
    AA --> BB{Formatting Method}
    BB -->|QScintilla| CC[Syntax Highlighting]
    BB -->|BeautifulSoup| DD[Pretty Print]
    BB -->|Basic| EE[Manual Formatting]
    
    CC --> FF[Display Formatted Source]
    DD --> FF
    EE --> FF
    
    FF --> GG[Save/Export Options]
    
    P -->|Tab Management| HH[Tab Operations]
    HH --> II{Tab Action}
    II -->|New Tab| JJ[Create New BrowserTab]
    II -->|Close Tab| KK[Remove Tab]
    II -->|Switch Tab| LL[Update UI State]
    
    JJ --> I
    KK --> MM{Last Tab?}
    MM -->|Yes| NN[Load Blank Page]
    MM -->|No| OO[Remove Tab Widget]
    
    LL --> PP[Update Address Bar]
    LL --> QQ[Update Window Title]
    LL --> RR[Update Source Button State]
    
    P -->|Navigation| SS[Browser Navigation]
    SS --> TT{Navigation Type}
    TT -->|Back| UU[webview.back]
    TT -->|Forward| VV[webview.forward]
    TT -->|Reload| WW[webview.reload]
    TT -->|Home| XX[Load Google]
    
    UU --> L
    VV --> L
    WW --> L
    XX --> L
    
    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style I fill:#e8f5e8
    style T fill:#fff3e0
    style Z fill:#fce4ec
    style HH fill:#f1f8e9

@enduml
```
# MyBro Browser - Architecture & Flow Documentation

## Application Structure

```
MyBro Browser Application
├── MyBrowser (QMainWindow)
│   ├── Toolbar
│   │   ├── Navigation Buttons (Back, Forward, Reload, Home)
│   │   ├── AddressBar (Custom QLineEdit)
│   │   ├── Source Button (QToolButton)
│   │   ├── User Button (QToolButton)
│   │   └── System Button (QToolButton)
│   ├── Tab Widget (QTabWidget)
│   │   └── BrowserTab(s) (QWidget)
│   │       └── QWebEngineView
│   └── Status Bar (QStatusBar)
```

## Main Application Flow

### 1. Application Initialization
```
[Start] → [Create MyBrowser Window] → [Setup UI Components]
                                    ├── Setup Window Position
                                    ├── Create Toolbar
                                    ├── Create Tab Widget
                                    ├── Create Status Bar
                                    └── Setup Keyboard Shortcuts
```

### 2. Tab Management Flow
```
[User Action] → [Tab Operation]
                ├── New Tab → [Create BrowserTab] → [Add to TabWidget]
                ├── Close Tab → [Check if Last Tab] → [Remove/Reset]
                └── Switch Tab → [Update UI State]
```

### 3. Page Navigation Flow
```
[User Input] → [Address Bar/Navigation Button]
             ↓
[Parse URL] → [Load Page] → [Page Load Event]
                           ├── Success → [Enable Source Button]
                           └── Failure → [Keep Source Button Disabled]
```

### 4. Source Code Viewing Flow
```
[Source Button Click] → [Get Page Source via JavaScript]
                      ↓
[Retrieve HTML] → [Check File Size]
                ├── Large (>5MB) → [Show Warning] → [User Choice]
                └── Normal → [Show Source Viewer]
                           ↓
[Source Dialog] → [Format Options]
                ├── QScintilla → [Syntax Highlighting]
                ├── BeautifulSoup → [Pretty Print HTML]
                └── Basic → [Manual Formatting]
                          ↓
[Display Formatted Source] → [Save/Export Options]
```

## Key Classes and Their Responsibilities

### MyBrowser (Main Window)
- **Purpose**: Main application window and controller
- **Key Methods**:
  - `setupToolbar()`: Creates navigation toolbar
  - `addBrowserTab()`: Creates new browser tabs
  - `viewPageSource()`: Initiates source code viewing
  - `navigateToUrl()`: Handles URL navigation
  - `setupShortcuts()`: Configures keyboard shortcuts

### BrowserTab (Individual Tab)
- **Purpose**: Manages individual browser tab with web view
- **Key Methods**:
  - `get_page_source()`: Extracts HTML via JavaScript
  - `show_source_viewer()`: Displays source code dialog
  - `format_html()`: Formats HTML with various methods
  - `save_source_to_file()`: Saves source to file system

### AddressBar (Custom Input)
- **Purpose**: Styled URL input field
- **Features**: Custom styling, placeholder text, focus handling

## Source Code Processing Pipeline

### 1. Extraction
```javascript
// JavaScript executed in web page context
document.documentElement.outerHTML
```

### 2. Size Validation
- Check if source > 5MB (potential obfuscation detection)
- Show warning dialog for large files

### 3. Formatting Options (Priority Order)
1. **QScintilla** (if available)
   - Syntax highlighting
   - Line numbers
   - Code folding

2. **BeautifulSoup** (if available)
   - Pretty print HTML
   - Proper indentation
   - Tag balancing

3. **Basic Formatter** (fallback)
   - Manual tag formatting
   - Custom indentation logic
   - JavaScript/CSS content preservation

### 4. Display & Export
- Show in dialog with formatting options
- Save to file with timestamp
- Open in external editor

## Event Handling

### Page Load Events
```
URL Change → Reset page loaded state
Load Started → Disable source button
Load Finished → Enable source button (if successful)
```

### Tab Events
```
Tab Switch → Update address bar, window title, source button state
Tab Close → Remove tab or reset to blank page (if last tab)
Tab Add → Create new BrowserTab instance
```

### User Input Events
```
Address Bar Enter → Parse URL → Navigate
Keyboard Shortcuts → Execute corresponding actions
Button Clicks → Execute specific functions
```

## Multi-Monitor Support

```
Application Start → Detect Screens
                  ├── Single Monitor → Maximize window
                  └── Multiple Monitors → Position on primary screen
                                        → Maximize window
```

## Logging System

- **File**: `logs/mybro.log`
- **Rotation**: Daily at midnight
- **Retention**: 7 days
- **Compression**: ZIP format
- **Levels**: DEBUG, INFO, WARNING, ERROR

## Dependencies

### Required
- PySide6 (Qt framework)
- loguru (logging)

### Optional (Enhanced Features)
- PyQt6-QScintilla (syntax highlighting)
- beautifulsoup4 (HTML formatting)
- jsbeautifier (JavaScript formatting)

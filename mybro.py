#!/usr/bin/env python3
# -*- coding:utf-8 -*-

################################################################################
# File:    d:\Code\Python\alan\2nd\mybro.py
# Project: d:\Code\Python\alan\2nd
# Created Date: Sunday, June 22nd 2025, 15:43:11
# Author: Jeremy
# ------------------------------------------------------------------------------
# Last Modified: Wednesday, June 25th 2025, 06:58:22
# Modified By: Jeremy
# ------------------------------------------------------------------------------
# Copyright (c)   2025 None
# ---------------- ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ----------------
# HISTORY: 
# Date                	By             	Comments
# --------------------	---------------	---------------------------------------
################################################################################

#!/usr/bin/env python3
# -*- coding:utf-8 -*-

import os
import sys
from datetime import datetime
from pathlib import Path

from loguru import logger
from PySide6.QtCore import QDateTime, QSize, Qt, QTimer, QUrl, Signal, Slot
from PySide6.QtGui import (QAction, QDesktopServices, QFont, QGuiApplication,
                           QIcon, QKeySequence, QPainter, QPixmap)
from PySide6.QtWebEngineCore import QWebEnginePage, QWebEngineProfile
from PySide6.QtWebEngineWidgets import QWebEngineView
from PySide6.QtWidgets import (QApplication, QDialog, QFrame, QHBoxLayout,
                               QLabel, QLineEdit, QMainWindow, QMenu, QMenuBar,
                               QMessageBox, QPushButton, QScrollArea,
                               QSizePolicy, QStatusBar, QTabWidget, QTextEdit,
                               QToolBar, QToolButton, QVBoxLayout, QWidget)

# Try to import QScintilla for code editing
try:
    from PyQt6.Qsci import QsciLexerHTML, QsciScintilla
    HAS_QSCINTILLA = True
    logger.info("QScintilla is available for code formatting")
except ImportError:
    HAS_QSCINTILLA = False
    logger.warning("QScintilla not available. Install with: pip install PyQt6-QScintilla")

# Configure logging
log_dir = Path(__file__).parent / "logs"
log_dir.mkdir(exist_ok=True)
log_path = log_dir / "mybro.log"

# Note: For advanced HTML formatting, install these packages:
# pip install beautifulsoup4
# pip install PyQt6-QScintilla
# pip install jsbeautifier
# pip install pytidylib

# Remove default logger and configure custom logger
logger.remove()
logger.add(
    log_path,
    format="{time} - {name} - {line} - {level} - {process} - {thread} - {module} - {function} - {exception} - {message}",
    rotation="00:00",  # Rotate at midnight
    retention="7 days",  # Keep logs for 7 days
    compression="zip",  # Compress rotated logs
    level="DEBUG"
)

class AddressBar(QLineEdit):
    """Custom address bar with integrated features"""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setPlaceholderText("Search or enter website name")
        
        # Style the address bar
        self.setStyleSheet("""
            QLineEdit {
                border-radius: 15px;
                padding: 5px 10px;
                background-color: #f1f3f4;
                selection-background-color: #cce8ff;
                font-size: 14px;
                min-height: 30px;
            }
            QLineEdit:focus {
                background-color: white;
                border: 1px solid #4285f4;
            }
        """)


class BrowserTab(QWidget):
    """Individual browser tab component"""
    titleChanged = Signal(str)
    urlChanged = Signal(QUrl)
    loadFinished = Signal(bool)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.layout = QVBoxLayout(self)
        self.layout.setContentsMargins(0, 0, 0, 0)
        self.layout.setSpacing(0)
        
        # Create web view
        self.webview = QWebEngineView()
        self.webview.page().urlChanged.connect(self.handleUrlChanged)
        self.webview.page().titleChanged.connect(self.handleTitleChanged)
        self.webview.page().loadFinished.connect(self.handleLoadFinished)
        
        # Add to layout
        self.layout.addWidget(self.webview)
        
        # Set default URL
        self.webview.setUrl(QUrl("https://www.google.com"))
        
        # Track page load state
        self.is_page_loaded = False
    
    def back(self):
        self.webview.back()
        
    def current_title(self):
        return self.webview.page().title()
        
    def current_url(self):
        return self.webview.url()
    
    def forward(self):
        self.webview.forward()
    
    
    def getPageSource(self):
        """Get the source code of the current page"""
        current_tab = self.currentTab()
        if current_tab and current_tab.is_page_loaded:
            current_tab.get_page_source()
        else:
            logger.warning("Cannot get source: no active tab or page not loaded")
        
    def get_page_source(self):
        """Get the HTML source of the current page"""
        if not self.is_page_loaded:
            logger.warning("Cannot get source: page not fully loaded")
            return None
            
        # Use JavaScript to get the page source
        self.webview.page().runJavaScript(
            "(function() { return document.documentElement.outerHTML; })()",
            self._handle_source_result
        )
    
    def _is_minified_or_obfuscated(self, html):
        """Detect if HTML is minified or potentially obfuscated"""
        import re

        # Check for minification indicators
        lines = html.split('\n')
        
        # If it's all on one line or very few lines, it's likely minified
        if len(lines) <= 5:
            return True
        
        # Check for very long lines (typical in minified code)
        for line in lines:
            if len(line) > 1000:
                return True
        
        # Check for obfuscation indicators (lots of escaped characters, hex codes, etc.)
        obfuscation_patterns = [
            r'\\x[0-9a-f]{2}',  # Hex escape sequences
            r'\\u[0-9a-f]{4}',  # Unicode escape sequences
            r'eval\s*\(',        # eval() calls
            r'document\.write\s*\('  # document.write() calls
        ]
        
        for pattern in obfuscation_patterns:
            if re.search(pattern, html, re.IGNORECASE):
                return True
        
        return False

    def _handle_source_result(self, html):
        """Handle the HTML source result from JavaScript"""
        if html:
            # Store the source code in a variable
            self.current_page_source = html
            logger.info(f"Page source retrieved, length: {len(html)} characters")
            
            # Check if the source is suspiciously large (potential trap)
            if len(html) > 5000000:  # 5MB limit
                logger.warning(f"Very large page source detected: {len(html)} bytes. Possible obfuscation.")
                
                # Show warning to user
                result = QMessageBox.question(
                    self,
                    "Large Source Code Detected",
                    f"The page source is very large ({len(html)/1000000:.1f} MB).\n\n"
                    "Do you want to continue loading it?",
                    QMessageBox.Yes | QMessageBox.No,
                    QMessageBox.No
                )
                
                if result == QMessageBox.No:
                    return
            
            # Show the source viewer directly without checking for minification
            self.show_source_viewer()
        else:
            logger.error("Failed to retrieve page source")
            QMessageBox.warning(
                self,
                "Error",
                "Failed to retrieve page source"
            )

    def show_source_viewer(self, skip_formatting=False):
        """Show a dialog with the source code"""
        if hasattr(self, 'current_page_source'):
            # Create a simple dialog to show the source
            dialog = QDialog(self)
            dialog.setWindowTitle("Page Source")
            dialog.resize(1000, 700)
            
            main_layout = QVBoxLayout(dialog)
            main_layout.setContentsMargins(5, 5, 5, 5)
            main_layout.setSpacing(5)
            
            # Create toolbar
            toolbar = QHBoxLayout()
            toolbar.setContentsMargins(0, 0, 0, 0)
            toolbar.setSpacing(5)
            
            # Format button
            format_button = QPushButton("Format HTML")
            format_button.setToolTip("Format and syntax highlight the HTML")
            toolbar.addWidget(format_button)
            
            # Add a spacer to push the next buttons to the right
            toolbar.addStretch(1)
            
            # Save button
            save_button = QPushButton("Save to File")
            save_button.setToolTip("Save source code to a file")
            save_button.clicked.connect(lambda: self.save_source_to_file())
            toolbar.addWidget(save_button)
            
            # Close button
            close_button = QPushButton("Close")
            close_button.clicked.connect(dialog.accept)
            toolbar.addWidget(close_button)
            
            # Add toolbar to main layout
            main_layout.addLayout(toolbar)
            
            # Add a separator line
            separator = QFrame()
            separator.setFrameShape(QFrame.HLine)
            separator.setFrameShadow(QFrame.Sunken)
            main_layout.addWidget(separator)
            
            # Create a text editor to display the source
            if HAS_QSCINTILLA:
                # Use QScintilla for better code formatting and syntax highlighting
                text_edit = QsciScintilla()
                
                # Configure the editor
                text_edit.setUtf8(True)
                text_edit.setMarginType(0, QsciScintilla.NumberMargin)
                text_edit.setMarginWidth(0, "0000")
                text_edit.setMarginsForegroundColor(Qt.gray)
                
                # Set up HTML lexer for syntax highlighting
                lexer = QsciLexerHTML()
                text_edit.setLexer(lexer)
                
                # Set monospace font
                font = QFont("Courier New", 10)
                lexer.setFont(font)
                
                # Set the text - just add line numbers initially without formatting
                if skip_formatting:
                    text_edit.setText(self.current_page_source)
                else:
                    # Add basic line breaks for readability
                    html = self.current_page_source
                    lines = html.split('\n')
                    if len(lines) <= 1:  # If it's all on one line, add some basic breaks
                        html = html.replace('>', '>\n').replace('<', '\n<')
                        lines = html.split('\n')
                    
                    text_edit.setText(html)
                
                # Connect format button
                format_button.clicked.connect(lambda: self.format_html_with_qscintilla(text_edit))
            else:
                # Fallback to QTextEdit
                text_edit = QTextEdit()
                text_edit.setReadOnly(True)
                
                # Just add line numbers initially without formatting
                if skip_formatting:
                    lines = self.current_page_source.split('\n')
                    if len(lines) <= 1:  # If it's all on one line, show raw text
                        text_edit.setPlainText("1 " + self.current_page_source[:1000] + 
                                              "\n(Source is very large. Click 'Format HTML' to format it)")
                    else:
                        numbered_text = '\n'.join([f"{i+1:4d} {line}" for i, line in enumerate(lines)])
                        text_edit.setPlainText(numbered_text)
                else:
                    # Add basic line breaks for readability
                    html = self.current_page_source
                    lines = html.split('\n')
                    if len(lines) <= 1:  # If it's all on one line, add some basic breaks
                        html = html.replace('>', '>\n').replace('<', '\n<')
                        lines = html.split('\n')
                    
                    numbered_text = '\n'.join([f"{i+1:4d} {line}" for i, line in enumerate(lines)])
                    text_edit.setPlainText(numbered_text)
                
                text_edit.setLineWrapMode(QTextEdit.NoWrap)
                
                # Add a monospace font for better code viewing
                font = QFont("Courier New", 10)
                text_edit.setFont(font)
                
                # Connect format button
                format_button.clicked.connect(lambda: self.format_html(text_edit))
            
            main_layout.addWidget(text_edit)
            
            # Add a status bar with length and line count information
            status_layout = QHBoxLayout()
            status_layout.addStretch(1)
            
            # Get line count
            line_count = len(self.current_page_source.split('\n'))
            
            # Format file size
            size_bytes = len(self.current_page_source)
            if size_bytes < 1024:
                size_str = f"{size_bytes} bytes"
            elif size_bytes < 1024 * 1024:
                size_str = f"{size_bytes/1024:.1f} KB"
            else:
                size_str = f"{size_bytes/(1024*1024):.1f} MB"
            
            length_label = QLabel(f"Length: {len(self.current_page_source)} characters | Lines: {line_count} | Size: {size_str}")
            length_label.setStyleSheet("color: #666;")
            status_layout.addWidget(length_label)
            
            main_layout.addLayout(status_layout)
            
            # Set dialog layout
            dialog.setLayout(main_layout)
            dialog.exec()
        else:
            logger.warning("No source code available to display")

    def format_html_with_qscintilla(self, editor):
        """Format HTML using QScintilla's built-in capabilities"""
        try:
            # Get the source code
            html = self.current_page_source
            
            # Ensure tags are balanced
            html = self._ensure_balanced_tags(html)
            
            # Use BeautifulSoup to format the HTML
            try:
                from bs4 import BeautifulSoup
                soup = BeautifulSoup(html, 'html.parser')
                formatted_html = soup.prettify()
                
                # Set the formatted text back to the editor
                editor.setText(formatted_html)
                logger.info("HTML source formatted with BeautifulSoup")
                return
            except ImportError:
                logger.warning("BeautifulSoup not available for QScintilla formatting")
            
            # Use our custom formatter as fallback
            formatted_html = self._apply_basic_html_formatting(html)
            # Remove line numbers for QScintilla (it has its own)
            formatted_html = '\n'.join([line[5:] for line in formatted_html.split('\n')])
            editor.setText(formatted_html)
            logger.info("HTML source formatted with basic formatter")
            
        except Exception as e:
            logger.error(f"Error formatting HTML with QScintilla: {e}")
            QMessageBox.warning(
                self,
                "Formatting Error",
                f"Failed to format HTML: {e}"
            )

    def _ensure_balanced_tags(self, html):
        """Ensure HTML tags are properly balanced"""
        import re
        from html.parser import HTMLParser
        
        class TagCounter(HTMLParser):
            def __init__(self):
                super().__init__()
                self.tag_stack = []
                self.void_elements = {
                    'area', 'base', 'br', 'col', 'embed', 'hr', 'img', 'input',
                    'link', 'meta', 'param', 'source', 'track', 'wbr'
                }
                
            def handle_starttag(self, tag, attrs):
                if tag not in self.void_elements:
                    self.tag_stack.append(tag)
                    
            def handle_endtag(self, tag):
                if self.tag_stack and self.tag_stack[-1] == tag:
                    self.tag_stack.pop()
                elif tag in self.tag_stack:
                    # Tag is in stack but not at the top - mismatched nesting
                    # Remove it from wherever it is
                    self.tag_stack.remove(tag)
        
        # Parse the HTML to check tag balance
        parser = TagCounter()
        try:
            parser.feed(html)
        except Exception as e:
            logger.warning(f"HTML parsing error: {e}")
        
        # If we have unbalanced tags, try to fix them
        if parser.tag_stack:
            logger.warning(f"Unbalanced HTML tags detected: {parser.tag_stack}")
            
            # Add missing closing tags
            for tag in reversed(parser.tag_stack):
                html += f"</{tag}>"
                
            logger.info("Added missing closing tags to balance HTML")
        
        return html

    def format_html(self, text_edit):
        """Format HTML using the best available method"""
        if not hasattr(self, 'current_page_source'):
            return
        
        html = self.current_page_source
        
        # Show a temporary message
        text_edit.setPlainText("Formatting HTML, please wait...")
        QApplication.processEvents()  # Update the UI
        
        try:
            # Ensure tags are balanced
            html = self._ensure_balanced_tags(html)
            
            # Try BeautifulSoup first
            try:
                from bs4 import BeautifulSoup
                soup = BeautifulSoup(html, 'html.parser')
                formatted_html = soup.prettify()
                
                # Add line numbers
                lines = formatted_html.split('\n')
                numbered_lines = []
                for i, line in enumerate(lines):
                    line_num = f"{i+1:4d} {line}"
                    numbered_lines.append(line_num)
                    
                text_edit.setPlainText('\n'.join(numbered_lines))
                logger.info("HTML formatted with BeautifulSoup")
                return
            except ImportError:
                logger.warning("BeautifulSoup not installed. Using basic HTML formatting.")
            
            # Use our basic formatter as fallback
            formatted_html = self._apply_basic_html_formatting(html)
            text_edit.setPlainText(formatted_html)
            logger.info("HTML source formatted and highlighted")
            
        except Exception as e:
            logger.error(f"Error formatting HTML: {e}")
            # If all formatting fails, just show the raw HTML with line numbers
            lines = html.split('\n')
            if len(lines) <= 1:  # If it's all on one line, try to break it up
                html = html.replace('>', '>\n').replace('<', '\n<')
                lines = html.split('\n')
                
            numbered_lines = []
            for i, line in enumerate(lines):
                line_num = f"{i+1:4d} {line}"
                numbered_lines.append(line_num)
                
            text_edit.setPlainText('\n'.join(numbered_lines))

    def _apply_basic_html_formatting(self, html):
        """Apply basic HTML formatting as a fallback"""
        import re

        # Create a copy of the original HTML
        formatted_html = html
        
        # First, insert line breaks at logical points in the HTML
        # Add line breaks after closing tags
        formatted_html = re.sub(r'>', '>\n', formatted_html)
        
        # Add line breaks before opening tags
        formatted_html = re.sub(r'<', '\n<', formatted_html)
        
        # Remove extra blank lines
        formatted_html = re.sub(r'\n\s*\n', '\n', formatted_html)
        
        # Parse and format the HTML with proper indentation
        return self._format_html_with_proper_indentation(formatted_html)

    def _format_html_with_proper_indentation(self, html):
        """Format HTML with proper indentation, treating script content as a block"""
        import re
        
        lines = html.split('\n')
        clean_lines = []
        
        # First pass: clean up lines
        for line in lines:
            line = line.strip()
            if line:
                clean_lines.append(line)
        
        # Second pass: apply indentation
        result = []
        indent = 0
        in_script = False
        script_content = []
        
        # Define void elements (tags that don't need closing)
        void_elements = {'area', 'base', 'br', 'col', 'embed', 'hr', 'img', 'input', 
                        'link', 'meta', 'param', 'source', 'track', 'wbr'}
        
        i = 0
        while i < len(clean_lines):
            line = clean_lines[i]
            
            # Handle script tags specially
            if re.match(r'^<script', line) and not line.endswith('</script>'):
                # Start of script tag
                script_start = line
                script_content = []
                in_script = True
                
                # Add the opening script tag with proper indentation
                result.append('  ' * indent + script_start)
                
                # Increase indent for script content
                indent += 1
                
                # Collect all content until the closing script tag
                i += 1
                while i < len(clean_lines) and not re.match(r'^</script>', clean_lines[i]):
                    script_content.append(clean_lines[i])
                    i += 1
                
                # If we found the closing tag
                if i < len(clean_lines):
                    # Add script content with indentation (but don't try to format it)
                    for content_line in script_content:
                        result.append('  ' * indent + content_line)
                    
                    # Decrease indent for closing script tag
                    indent -= 1
                    
                    # Add the closing script tag
                    result.append('  ' * indent + clean_lines[i])
                    in_script = False
                
                i += 1
                continue
            
            # Handle closing tags
            if re.match(r'^</', line):
                tag_name = re.match(r'^</(\w+)', line).group(1).lower()
                
                # Special handling for html tag
                if tag_name == 'html':
                    indent = 0
                else:
                    # Normal closing tags decrease indent
                    indent = max(0, indent - 1)
                
                # Add the line with proper indentation
                result.append('  ' * indent + line)
            
            # Handle opening tags
            elif re.match(r'^<\w', line):
                # Extract tag name
                tag_name = re.match(r'^<(\w+)', line).group(1).lower()
                
                # Add the line with proper indentation
                result.append('  ' * indent + line)
                
                # Don't increase indent for void elements or self-closing tags
                if tag_name not in void_elements and not line.endswith('/>') and not re.search(r'</\w+>$', line):
                    # Special handling for html tag
                    if tag_name == 'html':
                        indent = 1
                    else:
                        # Normal opening tags increase indent
                        indent += 1
            
            # Handle other lines
            else:
                result.append('  ' * indent + line)
            
            i += 1
        
        # Add line numbers
        formatted_html = '\n'.join(result)
        lines = formatted_html.split('\n')
        numbered_lines = []
        
        for i, line in enumerate(lines):
            line_num = f"{i+1:4d} {line}"
            numbered_lines.append(line_num)
        
        return '\n'.join(numbered_lines)

    def _format_javascript(self, js_code):
        """Format JavaScript code with proper indentation"""
        import re

        # Try to use jsbeautifier if available
        try:
            import jsbeautifier
            options = jsbeautifier.default_options()
            options.indent_size = 2
            options.indent_char = ' '
            options.preserve_newlines = True
            options.max_preserve_newlines = 2
            options.brace_style = 'collapse'
            
            formatted_js = jsbeautifier.beautify(js_code, options)
            return '\n' + formatted_js + '\n'
        except ImportError:
            logger.warning("jsbeautifier not available. Using basic JS formatting.")
        
        # Basic JavaScript formatting as fallback
        # Replace common JavaScript patterns with line breaks
        js_code = js_code.replace(';', ';\n')
        js_code = js_code.replace('{', '{\n')
        js_code = js_code.replace('}', '}\n')
        js_code = re.sub(r'\)\s*{', ') {\n', js_code)
        js_code = re.sub(r'else\s*{', 'else {\n', js_code)
        
        # Add line breaks for control structures
        js_code = re.sub(r'(if|for|while|switch)\s*\(', '\g<1> (', js_code)
        
        # Add line breaks for function declarations
        js_code = re.sub(r'function\s+(\w+)\s*\(', 'function \g<1>(', js_code)
        
        # Remove extra blank lines
        js_code = re.sub(r'\n\s*\n', '\n', js_code)
        
        # Indent the code using a stack-based approach
        lines = js_code.split('\n')
        indent = 0
        result = []
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            # Decrease indent for closing braces
            if line.startswith('}'):
                indent = max(0, indent - 1)
            
            # Add indentation
            if line:
                result.append('  ' * indent + line)
            
            # Increase indent for opening braces
            if line.endswith('{'):
                indent += 1
        
        return '\n' + '\n'.join(result) + '\n'

    def _format_css(self, css_code):
        """Format CSS code with proper indentation"""
        import re

        # Replace common CSS patterns with line breaks
        css_code = css_code.replace(';', ';\n')
        css_code = css_code.replace('{', ' {\n')
        css_code = css_code.replace('}', '}\n')
        
        # Remove extra blank lines
        css_code = re.sub(r'\n\s*\n', '\n', css_code)
        
        # Indent the code using a stack-based approach
        lines = css_code.split('\n')
        indent = 0
        result = []
        brace_stack = []
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            # Handle closing braces
            if line.startswith('}'):
                if brace_stack:
                    brace_stack.pop()
                    indent = max(0, indent - 1)
            
            # Add indentation
            if line:
                result.append('  ' * indent + line)
            
            # Handle opening braces
            if line.endswith('{'):
                brace_stack.append('{')
                indent += 1
        
        return '\n' + '\n'.join(result) + '\n'

    def save_source_to_file(self):
        """Save the current page source to a file"""
        if hasattr(self, 'current_page_source'):
            try:
                # Create a temporary file to save the source
                temp_dir = Path(__file__).parent / "temp"
                temp_dir.mkdir(exist_ok=True)
                
                # Create filename based on current URL and timestamp
                url = self.webview.url().host()
                if not url:
                    url = "page"
                timestamp = QDateTime.currentDateTime().toString("yyyyMMdd_hhmmss")
                filename = f"{url}_{timestamp}.html"
                filepath = temp_dir / filename
                
                # Save the HTML to file
                with open(filepath, 'w', encoding='utf-8') as f:
                    f.write(self.current_page_source)
                logger.info(f"Page source saved to: {filepath}")
                
                # Show success message
                QMessageBox.information(
                    self, 
                    "Source Code Saved",
                    f"Page source saved to:\n{filepath}"
                )
                
            except Exception as e:
                logger.error(f"Error saving page source: {e}")
                QMessageBox.critical(
                    self,
                    "Error",
                    f"Failed to save page source: {e}"
                )
        else:
            logger.warning("No source code available to save")

    def open_in_external_editor(self):
        """Open the source code in an external editor"""
        if hasattr(self, 'current_page_source'):
            try:
                # Create a temporary file
                temp_dir = Path(__file__).parent / "temp"
                temp_dir.mkdir(exist_ok=True)
                
                # Create filename based on current URL and timestamp
                url = self.webview.url().host()
                if not url:
                    url = "page"
                timestamp = QDateTime.currentDateTime().toString("yyyyMMdd_hhmmss")
                filename = f"{url}_{timestamp}.html"
                filepath = temp_dir / filename
                
                # Save the HTML to file
                with open(filepath, 'w', encoding='utf-8') as f:
                    f.write(self.current_page_source)
                
                # Try multiple methods to open the file
                success = False
                
                # Method 1: Use QDesktopServices
                try:
                    success = QDesktopServices.openUrl(QUrl.fromLocalFile(str(filepath)))
                    if success:
                        logger.info(f"Opened source code with QDesktopServices: {filepath}")
                    else:
                        logger.warning(f"QDesktopServices failed to open: {filepath}")
                except Exception as e:
                    logger.error(f"Error using QDesktopServices: {e}")
                
                # Method 2: Use platform-specific commands if QDesktopServices failed
                if not success:
                    import platform
                    import subprocess
                    
                    system = platform.system()
                    try:
                        if system == 'Windows':
                            os.startfile(str(filepath))
                            success = True
                            logger.info(f"Opened source code with os.startfile: {filepath}")
                        elif system == 'Darwin':  # macOS
                            subprocess.run(['open', str(filepath)], check=True)
                            success = True
                            logger.info(f"Opened source code with 'open' command: {filepath}")
                        else:  # Linux and other Unix-like
                            subprocess.run(['xdg-open', str(filepath)], check=True)
                            success = True
                            logger.info(f"Opened source code with 'xdg-open' command: {filepath}")
                    except Exception as e:
                        logger.error(f"Error opening file with platform-specific method: {e}")
                
                # If all external methods failed, show the source in a new dialog
                if not success:
                    logger.warning("External editor methods failed, showing source in internal viewer")
                    self.show_formatted_source_in_dialog()
                else:
                    # Show a success message
                    QMessageBox.information(
                        self,
                        "File Opened",
                        f"The source code has been saved to and opened from:\n{filepath}"
                    )
                    
            except Exception as e:
                logger.error(f"Error in open_in_external_editor: {e}")
                QMessageBox.critical(
                    self,
                    "Error",
                    f"Failed to open source code in external editor: {e}"
                )
        else:
            logger.warning("No source code available to open")

    def show_formatted_source_in_dialog(self):
        """Show formatted source in a dialog when external editor fails"""
        if not hasattr(self, 'current_page_source'):
            return
        
        # Create a dialog
        dialog = QDialog(self)
        dialog.setWindowTitle("HTML Source Viewer")
        dialog.resize(1000, 700)
        
        # Create layout
        layout = QVBoxLayout(dialog)
        
        # Create a QTextEdit with HTML formatting
        text_edit = QTextEdit()
        text_edit.setReadOnly(True)
        
        # Format the HTML for better readability
        formatted_html = self._format_html_for_display(self.current_page_source)
        
        # Set the formatted HTML
        text_edit.setPlainText(formatted_html)
        
        # Use monospace font
        font = QFont("Courier New", 10)
        text_edit.setFont(font)
        text_edit.setLineWrapMode(QTextEdit.NoWrap)
        
        # Add to layout
        layout.addWidget(text_edit)
        
        # Add close button
        button_layout = QHBoxLayout()
        close_button = QPushButton("Close")
        close_button.clicked.connect(dialog.accept)
        button_layout.addStretch(1)
        button_layout.addWidget(close_button)
        layout.addLayout(button_layout)
        
        # Show dialog
        dialog.exec()

    def _format_html_for_display(self, html):
        """Format HTML for better readability in the text editor"""
        return self._apply_basic_html_formatting(html)

    def handleLoadFinished(self, success):
        self.is_page_loaded = success
        self.loadFinished.emit(success)

        if success:
            logger.info(f"Page loaded: {self.webview.url().toString()}")
        else:
            logger.error(f"Failed to load: {self.webview.url().toString()}")

        # Update analysis window snap button if available
        # Find the main browser window through the widget hierarchy
        main_window = self.window()
        if hasattr(main_window, 'analysis_window') and main_window.analysis_window:
            main_window.analysis_window.updateSnapButton(success, self.current_url())
    
    def handleTitleChanged(self, title):
        self.titleChanged.emit(title)
    
    def handleUrlChanged(self, url):
        # Reset page loaded state when URL changes
        self.is_page_loaded = False
        self.urlChanged.emit(url)
        
        # Emit loadFinished with False to hide the source button
        self.loadFinished.emit(False)
    
    def load(self, url):
        if not url.scheme():
            url = QUrl("https://" + url)
        self.webview.load(url)
    
    def reload(self):
        self.webview.reload()
    
    def stop(self):
        self.webview.stop()


class AnalysisWindow(QMainWindow):
    """Analysis window for second screen with full page screenshots"""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("MyBro Analysis Window")
        self.parent_browser = parent

        # Initialize screenshot counter for each domain
        self.domain_counters = {}

        # Create snap directories
        self.snap_dir = Path(__file__).parent / "snap"
        self.snap_dir.mkdir(exist_ok=True)

        # Setup UI
        self.setupUI()

        # Position on second screen
        self.positionOnSecondScreen()

    def setupUI(self):
        """Setup the analysis window UI"""
        # Create central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # Main layout
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)

        # Function button zone (100px height)
        self.function_zone = QFrame()
        self.function_zone.setFixedHeight(100)
        self.function_zone.setStyleSheet("""
            QFrame {
                background-color: #f0f0f0;
                border-bottom: 2px solid #ccc;
            }
        """)

        # Function zone layout
        function_layout = QHBoxLayout(self.function_zone)
        function_layout.setContentsMargins(10, 10, 10, 10)

        # Add function buttons
        self.snap_button = QPushButton("Take Full Page Snap")
        self.snap_button.setToolTip("Take a full page screenshot")
        self.snap_button.clicked.connect(self.takeFullPageSnap)
        self.snap_button.setEnabled(False)  # Disabled until page loads
        function_layout.addWidget(self.snap_button)

        self.clear_button = QPushButton("Clear Image")
        self.clear_button.setToolTip("Clear the current image")
        self.clear_button.clicked.connect(self.clearImage)
        function_layout.addWidget(self.clear_button)

        # Add stretch to push buttons to left
        function_layout.addStretch()

        # Status label
        self.status_label = QLabel("Ready")
        self.status_label.setStyleSheet("color: #666; font-size: 12px;")
        function_layout.addWidget(self.status_label)

        main_layout.addWidget(self.function_zone)

        # Image display area with scroll
        self.scroll_area = QScrollArea()
        self.scroll_area.setWidgetResizable(True)
        self.scroll_area.setStyleSheet("""
            QScrollArea {
                background-color: #ffffff;
                border: none;
            }
        """)

        # Image label
        self.image_label = QLabel()
        self.image_label.setAlignment(Qt.AlignCenter)
        self.image_label.setStyleSheet("""
            QLabel {
                background-color: #ffffff;
                border: 1px solid #ddd;
                color: #999;
                font-size: 14px;
            }
        """)
        self.image_label.setText("No image loaded\nFull page screenshot will appear here")
        self.image_label.setMinimumSize(800, 600)

        self.scroll_area.setWidget(self.image_label)
        main_layout.addWidget(self.scroll_area)

    def positionOnSecondScreen(self):
        """Position window on second screen if available"""
        screens = QGuiApplication.screens()
        logger.info(f"Analysis window: Detected {len(screens)} screen(s)")

        if len(screens) > 1:
            # Use second screen (index 1)
            second_screen = screens[1]
            screen_geometry = second_screen.availableGeometry()

            logger.info(f"Second screen geometry: {screen_geometry.width()}x{screen_geometry.height()}")
            logger.info(f"Second screen position: ({screen_geometry.x()}, {screen_geometry.y()})")

            # Set window to full screen on second monitor
            self.setGeometry(screen_geometry)
            self.showMaximized()

            # Move to second screen
            self.move(screen_geometry.x(), screen_geometry.y())

            logger.info("Analysis window positioned on second screen")
        else:
            # Fallback: position on primary screen (right side)
            primary_screen = QGuiApplication.primaryScreen()
            screen_geometry = primary_screen.availableGeometry()

            # Use right half of primary screen
            width = screen_geometry.width() // 2
            height = screen_geometry.height()
            x = screen_geometry.x() + width
            y = screen_geometry.y()

            self.setGeometry(x, y, width, height)
            logger.info("Analysis window positioned on right half of primary screen")

    def updateSnapButton(self, enabled, url=None):
        """Update snap button state based on page load status"""
        self.snap_button.setEnabled(enabled)
        if enabled and url:
            domain = self.extractDomain(url)
            self.status_label.setText(f"Ready to snap: {domain}")
        else:
            self.status_label.setText("Waiting for page to load...")

    def extractDomain(self, url):
        """Extract domain from URL"""
        if isinstance(url, QUrl):
            return url.host() or "unknown"
        elif isinstance(url, str):
            from urllib.parse import urlparse
            parsed = urlparse(url)
            return parsed.netloc or "unknown"
        return "unknown"

    def getSerialNumber(self, domain):
        """Get and increment serial number for domain"""
        if domain not in self.domain_counters:
            self.domain_counters[domain] = 1
        else:
            self.domain_counters[domain] += 1
        return self.domain_counters[domain]

    def generateFilename(self, domain):
        """Generate filename with format: <domain>_<serialnum>_<datetime>.png"""
        serial_num = self.getSerialNumber(domain)
        now = datetime.now()
        # Format: yyyymmddThh:MM:SS-xxx (xxx is milliseconds)
        timestamp = now.strftime("%Y%m%dT%H:%M:%S") + f"-{now.microsecond//1000:03d}"
        return f"{domain}_{serial_num}_{timestamp}.png"

    def createDomainDirectory(self, domain):
        """Create domain directory structure: snap/<domain>/full/"""
        domain_dir = self.snap_dir / domain / "full"
        domain_dir.mkdir(parents=True, exist_ok=True)
        return domain_dir

    def takeFullPageSnap(self):
        """Take a full page screenshot"""
        if not self.parent_browser:
            logger.error("No parent browser available")
            return

        current_tab = self.parent_browser.currentTab()
        if not current_tab or not current_tab.is_page_loaded:
            logger.warning("No active tab or page not loaded")
            QMessageBox.warning(self, "Error", "No active tab or page not fully loaded")
            return

        url = current_tab.current_url()
        domain = self.extractDomain(url)

        self.status_label.setText(f"Taking screenshot of {domain}...")

        # Take screenshot of the web view
        webview = current_tab.webview

        # Get the full page size
        webview.page().runJavaScript(
            """
            (function() {
                return {
                    width: Math.max(document.body.scrollWidth, document.documentElement.scrollWidth),
                    height: Math.max(document.body.scrollHeight, document.documentElement.scrollHeight)
                };
            })()
            """,
            self.handlePageSize
        )

    def handlePageSize(self, size_info):
        """Handle the page size information and take screenshot"""
        if not size_info or not isinstance(size_info, dict):
            logger.error("Failed to get page size information")
            self.status_label.setText("Error: Could not get page dimensions")
            return

        current_tab = self.parent_browser.currentTab()
        if not current_tab:
            return

        webview = current_tab.webview
        url = current_tab.current_url()
        domain = self.extractDomain(url)

        # Create filename and directory
        filename = self.generateFilename(domain)
        domain_dir = self.createDomainDirectory(domain)
        filepath = domain_dir / filename

        # Take screenshot
        try:
            # Get current viewport size
            current_size = webview.size()

            # Set size to full page dimensions
            full_width = int(size_info.get('width', current_size.width()))
            full_height = int(size_info.get('height', current_size.height()))

            # Create pixmap with full page size
            pixmap = QPixmap(full_width, full_height)

            # Render the web view to pixmap
            webview.render(pixmap)

            # Save the screenshot
            success = pixmap.save(str(filepath), "PNG")

            if success:
                logger.info(f"Screenshot saved: {filepath}")
                self.status_label.setText(f"Screenshot saved: {filename}")

                # Display the screenshot in the image label
                self.displayImage(pixmap)

                # Show success message
                QMessageBox.information(
                    self,
                    "Screenshot Saved",
                    f"Full page screenshot saved to:\n{filepath}"
                )
            else:
                logger.error(f"Failed to save screenshot: {filepath}")
                self.status_label.setText("Error: Failed to save screenshot")
                QMessageBox.critical(
                    self,
                    "Error",
                    f"Failed to save screenshot to:\n{filepath}"
                )

        except Exception as e:
            logger.error(f"Error taking screenshot: {e}")
            self.status_label.setText(f"Error: {str(e)}")
            QMessageBox.critical(
                self,
                "Screenshot Error",
                f"Failed to take screenshot:\n{str(e)}"
            )

    def displayImage(self, pixmap):
        """Display the screenshot in the image label"""
        if pixmap and not pixmap.isNull():
            # Scale image to fit the scroll area while maintaining aspect ratio
            scroll_size = self.scroll_area.size()
            scaled_pixmap = pixmap.scaled(
                scroll_size.width() - 20,  # Leave some margin
                scroll_size.height() - 20,
                Qt.KeepAspectRatio,
                Qt.SmoothTransformation
            )

            self.image_label.setPixmap(scaled_pixmap)
            self.image_label.resize(scaled_pixmap.size())
        else:
            logger.warning("Invalid pixmap for display")

    def clearImage(self):
        """Clear the current image"""
        self.image_label.clear()
        self.image_label.setText("No image loaded\nFull page screenshot will appear here")
        self.status_label.setText("Image cleared")


class MyBrowser(QMainWindow):
    """Main browser window"""
    def __init__(self):
        super().__init__()
        self.setWindowTitle("MyBro Browser")
        self.resize(1200, 800)

        # Initialize analysis window
        self.analysis_window = None

        # Position window on main screen and maximize if multiple monitors
        screens = QGuiApplication.screens()
        logger.info(f"Detected {len(screens)} screen(s)")

        if len(screens) > 1:
            # Multiple monitors detected
            logger.info("Multiple monitors detected, positioning on primary screen")

            # Get the primary screen
            primary_screen = QGuiApplication.primaryScreen()

            # Get the geometry of the primary screen
            screen_geometry = primary_screen.availableGeometry()

            # Center the window on the primary screen
            window_geometry = self.geometry()
            x = (screen_geometry.width() - window_geometry.width()) // 2
            y = (screen_geometry.height() - window_geometry.height()) // 2

            # Move the window to the primary screen
            self.setGeometry(
                screen_geometry.x() + x,
                screen_geometry.y() + y,
                window_geometry.width(),
                window_geometry.height()
            )

            # Maximize the window
            self.showMaximized()
            logger.info("Window maximized on primary screen")

            # Create and show analysis window on second screen
            self.createAnalysisWindow()
        else:
            # Single monitor, just maximize
            self.showMaximized()
            logger.info("Single monitor detected, window maximized")

            # Create analysis window (will be positioned on right half of screen)
            self.createAnalysisWindow()
        
        # Set up central widget
        self.central = QWidget()
        self.setCentralWidget(self.central)
        self.layout = QVBoxLayout(self.central)
        self.layout.setContentsMargins(0, 0, 0, 0)
        self.layout.setSpacing(0)
        
        # Create toolbar
        self.setupToolbar()
        
        # Create tab widget
        self.tabs = QTabWidget()
        self.tabs.setTabsClosable(True)
        self.tabs.tabCloseRequested.connect(self.closeTab)
        self.tabs.currentChanged.connect(self.tabChanged)
        self.layout.addWidget(self.tabs)
        
        # Create status bar
        self.status = QStatusBar()
        self.setStatusBar(self.status)
        
        # Add first tab
        self.addBrowserTab()
        
        # Set up keyboard shortcuts
        self.setupShortcuts()

    def createAnalysisWindow(self):
        """Create and show the analysis window"""
        if not self.analysis_window:
            self.analysis_window = AnalysisWindow(self)
            self.analysis_window.show()
            logger.info("Analysis window created and shown")
        else:
            self.analysis_window.show()
            logger.info("Analysis window shown")

    def toggleAnalysisWindow(self):
        """Toggle the visibility of the analysis window"""
        if not self.analysis_window:
            self.createAnalysisWindow()
            self.analysis_button.setChecked(True)
        else:
            if self.analysis_window.isVisible():
                self.analysis_window.hide()
                self.analysis_button.setChecked(False)
                logger.info("Analysis window hidden")
            else:
                self.analysis_window.show()
                self.analysis_button.setChecked(True)
                logger.info("Analysis window shown")

    def positionOnMainScreen(self):
        """Position the window on the main screen and maximize if multiple monitors"""
        from PySide6.QtGui import QGuiApplication

        # Get all available screens
        screens = QGuiApplication.screens()
        logger.info(f"Detected {len(screens)} screen(s)")
        
        if len(screens) > 1:
            # Multiple monitors detected
            logger.info("Multiple monitors detected, positioning on primary screen")
            
            # Get the primary screen
            primary_screen = QGuiApplication.primaryScreen()
            
            # Get the geometry of the primary screen
            screen_geometry = primary_screen.availableGeometry()
            
            # Center the window on the primary screen
            window_geometry = self.geometry()
            x = (screen_geometry.width() - window_geometry.width()) // 2
            y = (screen_geometry.height() - window_geometry.height()) // 2
            
            # Move the window to the primary screen
            self.setGeometry(
                screen_geometry.x() + x,
                screen_geometry.y() + y,
                window_geometry.width(),
                window_geometry.height()
            )
            
            # Maximize the window
            self.showMaximized()
            logger.info("Window maximized on primary screen")
        else:
            # Single monitor, just maximize
            self.showMaximized()
            logger.info("Single monitor detected, window maximized")
    
    def setupToolbar(self):
        """Set up the browser toolbar"""
        # Create toolbar
        toolbar = QToolBar("Navigation")
        toolbar.setIconSize(QSize(16, 16))
        toolbar.setMovable(False)
        self.addToolBar(toolbar)
        
        # Back button
        back_action = QAction(QIcon.fromTheme("go-previous", QIcon("icons/back.png")), "Back", self)
        back_action.setShortcut(QKeySequence(Qt.Key_Back))
        back_action.triggered.connect(self.goBack)
        toolbar.addAction(back_action)
        
        # Forward button
        forward_action = QAction(QIcon.fromTheme("go-next", QIcon("icons/forward.png")), "Forward", self)
        forward_action.setShortcut(QKeySequence(Qt.Key_Forward))
        forward_action.triggered.connect(self.goForward)
        toolbar.addAction(forward_action)
        
        # Reload button
        reload_action = QAction(QIcon.fromTheme("view-refresh", QIcon("icons/reload.png")), "Reload", self)
        reload_action.setShortcut(QKeySequence.Refresh)
        reload_action.triggered.connect(self.reloadPage)
        toolbar.addAction(reload_action)
        
        # Home button
        home_action = QAction(QIcon.fromTheme("go-home", QIcon("icons/home.png")), "Home", self)
        home_action.setShortcut(QKeySequence(Qt.Key_HomePage))
        home_action.triggered.connect(self.goHome)
        toolbar.addAction(home_action)
        
        # Add separator
        toolbar.addSeparator()
        
        # Create a container widget for the address bar to control its size
        address_container = QWidget()
        address_layout = QHBoxLayout(address_container)
        address_layout.setContentsMargins(5, 0, 5, 0)
        address_layout.setSpacing(0)
        
        # Address bar
        self.address_bar = AddressBar()
        self.address_bar.returnPressed.connect(self.navigateToUrl)
        address_layout.addWidget(self.address_bar)
        
        # Set size policy to make address bar expand
        address_container.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        toolbar.addWidget(address_container)
        
        # Source button as QToolButton
        self.source_button = QToolButton()
        self.source_button.setText("Source")
        self.source_button.setToolTip("View page source (Ctrl+U)")
        self.source_button.clicked.connect(self.viewPageSource)
        self.source_button.setEnabled(False)  # Disabled until page loads
        toolbar.addWidget(self.source_button)

        # Analysis Window toggle button
        self.analysis_button = QToolButton()
        self.analysis_button.setText("Analysis")
        self.analysis_button.setToolTip("Toggle Analysis Window (Ctrl+A)")
        self.analysis_button.clicked.connect(self.toggleAnalysisWindow)
        self.analysis_button.setCheckable(True)
        self.analysis_button.setChecked(True)  # Initially shown
        toolbar.addWidget(self.analysis_button)
        
        # User button
        self.user_button = QToolButton()
        self.user_button.setText("User")
        self.user_button.setToolTip("User Account")
        self.user_button.setPopupMode(QToolButton.InstantPopup)
        
        # Create user menu
        user_menu = QMenu(self.user_button)
        user_menu.addAction("Sign In")
        user_menu.addAction("Register")
        user_menu.addSeparator()
        user_menu.addAction("Guest Mode")
        
        self.user_button.setMenu(user_menu)
        toolbar.addWidget(self.user_button)
        
        # System menu button (custom)
        self.system_button = SystemMenuButton()
        
        # Create system menu
        system_menu = QMenu(self.system_button)
        system_menu.addAction("Settings")
        system_menu.addAction("History")
        system_menu.addAction("Downloads")
        system_menu.addSeparator()
        system_menu.addAction("About MyBro")
        
        self.system_button.setMenu(system_menu)
        toolbar.addWidget(self.system_button)
        
        # Add keyboard shortcut for Source button
        source_shortcut = QAction(self)
        source_shortcut.setShortcut(QKeySequence("Ctrl+U"))
        source_shortcut.triggered.connect(self.viewPageSource)
        self.addAction(source_shortcut)

        # Add keyboard shortcut for Analysis Window toggle
        analysis_shortcut = QAction(self)
        analysis_shortcut.setShortcut(QKeySequence("Ctrl+A"))
        analysis_shortcut.triggered.connect(self.toggleAnalysisWindow)
        self.addAction(analysis_shortcut)
        
        # Create a widget to hold the toolbar for proper styling
        toolbar_widget = QWidget()
        toolbar_layout = QVBoxLayout(toolbar_widget)
        toolbar_layout.setContentsMargins(0, 5, 0, 5)
        toolbar_layout.addWidget(toolbar)
        
        # Add to main layout
        self.layout.addWidget(toolbar_widget)
    
    def addBrowserTab(self, url=None):
        """Add a new browser tab"""
        browser_tab = BrowserTab()
        browser_tab.urlChanged.connect(self.updateAddressBar)
        browser_tab.titleChanged.connect(self.updateTabTitle)
        browser_tab.loadFinished.connect(self.updateSourceButton)
        
        index = self.tabs.addTab(browser_tab, "New Tab")
        self.tabs.setCurrentIndex(index)
        
        if url:
            browser_tab.load(QUrl(url))
        
        logger.info(f"New tab created (index: {index})")
        return browser_tab
    
    def closeCurrentTab(self):
        """Close the current tab"""
        self.closeTab(self.tabs.currentIndex())
    
    def closeTab(self, index):
        """Close the tab at the given index"""
        if self.tabs.count() > 1:
            self.tabs.removeTab(index)
            logger.info(f"Tab closed (index: {index})")
        else:
            # Don't close the last tab, just load a blank page
            current_tab = self.currentTab()
            if current_tab:
                current_tab.load(QUrl("about:blank"))
                logger.info("Last tab reset to blank page")
    
    def currentTab(self):
        """Get the current browser tab"""
        if self.tabs.count() > 0:
            return self.tabs.currentWidget()
        return None
    
    def focusAddressBar(self):
        """Set focus to the address bar and select all text"""
        self.address_bar.setFocus()
        self.address_bar.selectAll()
    
    def viewPageSource(self):
        """View the source code of the current page"""
        current_tab = self.currentTab()
        if current_tab and current_tab.is_page_loaded:
            current_tab.get_page_source()
        else:
            logger.warning("Cannot get source: no active tab or page not loaded")
            QMessageBox.warning(
                self,
                "Error",
                "Cannot get source: page not fully loaded"
            )
    
    def goBack(self):
        """Navigate back in the current tab"""
        current_tab = self.currentTab()
        if current_tab:
            current_tab.back()
            logger.debug("Navigating back")
    
    def goForward(self):
        """Navigate forward in the current tab"""
        current_tab = self.currentTab()
        if current_tab:
            current_tab.forward()
            logger.debug("Navigating forward")
    
    def reloadPage(self):
        """Reload the current page"""
        current_tab = self.currentTab()
        if current_tab:
            current_tab.reload()
            logger.debug("Reloading page")
    
    def setupShortcuts(self):
        """Set up keyboard shortcuts"""
        # Navigation shortcuts
        self.addAction(QAction("Back", self, triggered=self.goBack, shortcut=QKeySequence("Alt+Left")))
        self.addAction(QAction("Forward", self, triggered=self.goForward, shortcut=QKeySequence("Alt+Right")))
        self.addAction(QAction("Reload", self, triggered=self.reloadPage, shortcut=QKeySequence("F5")))
        self.addAction(QAction("Focus Address Bar", self, triggered=self.focusAddressBar, shortcut=QKeySequence("Ctrl+L")))
        self.addAction(QAction("New Tab", self, triggered=self.addBrowserTab, shortcut=QKeySequence("Ctrl+T")))
        self.addAction(QAction("Close Tab", self, triggered=self.closeCurrentTab, shortcut=QKeySequence("Ctrl+W")))
        
        logger.info("Keyboard shortcuts initialized")
    
    def tabChanged(self, index):
        """Handle tab change event"""
        if index >= 0:
            tab = self.tabs.widget(index)
            if tab:
                self.updateAddressBar(tab.current_url())
                self.updateTabTitle(tab.current_title())
                self.updateSourceButton(tab.is_page_loaded)
                logger.debug(f"Switched to tab {index}: {tab.current_url().toString()}")
    
    def updateAddressBar(self, url):
        """Update the address bar with the current URL"""
        if hasattr(self, 'address_bar') and url:
            self.address_bar.setText(url.toString())
    
    def updateSourceButton(self, loaded):
        """Update the source button based on page load state"""
        if loaded:
            self.source_button.setEnabled(True)
            # Set green color for the Source button when page is loaded
            self.source_button.setStyleSheet("color: green; font-weight: bold;")
            logger.debug("Source button shown and enabled")
        else:
            self.source_button.setEnabled(False)
            self.source_button.setStyleSheet("")  # Reset style
            logger.debug("Source button hidden")
    
    def updateTabTitle(self, title):
        """Update the tab title"""
        if title and self.tabs.count() > 0:
            index = self.tabs.currentIndex()
            if index >= 0:
                self.tabs.setTabText(index, title if len(title) < 20 else title[:17] + "...")
                self.setWindowTitle(f"{title} - MyBro")

    def goHome(self):
        """Navigate to the home page (Google)"""
        current_tab = self.currentTab()
        if current_tab:
            current_tab.load(QUrl("https://www.google.com"))
            logger.info("Navigating to home page")

    def navigateToUrl(self):
        """Navigate to the URL in the address bar"""
        url_text = self.address_bar.text().strip()
        if url_text:
            url = QUrl(url_text)
            # Add scheme if missing
            if not url.scheme():
                url = QUrl("https://" + url_text)
            
            current_tab = self.currentTab()
            if current_tab:
                current_tab.load(url)
                logger.info(f"Loading URL: {url.toString()}")

class ProfileButton(QToolButton):
    """User profile button styled like Chrome"""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setText("User")
        self.setToolTip("User Profile")
        self.setPopupMode(QToolButton.InstantPopup)
        
        # Create menu
        self.menu = QMenu(self)
        self.setMenu(self.menu)
        
        # Add actions
        self.menu.addAction("Sign in")
        self.menu.addAction("Guest Mode")
        self.menu.addSeparator()
        self.menu.addAction("Manage Profiles")
        
        # Style the button
        self.setStyleSheet("""
            QToolButton {
                border-radius: 15px;
                padding: 5px 10px;
                background-color: #f1f3f4;
                font-size: 14px;
            }
            QToolButton:hover {
                background-color: #e0e0e0;
            }
            QToolButton::menu-indicator {
                image: none;
            }
        """)


class SystemMenuButton(QToolButton):
    """Custom system menu button with unique styling"""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setToolTip("System Menu")
        self.setPopupMode(QToolButton.InstantPopup)
        
        # Set fixed size for the button
        self.setFixedSize(30, 30)
        
        # Style the button
        self.setStyleSheet("""
            QToolButton {
                border: none;
                border-radius: 15px;
                background-color: transparent;
            }
            QToolButton:hover {
                background-color: #e0e0e0;
            }
            QToolButton::menu-indicator {
                image: none;
            }
        """)
    
    def paintEvent(self, event):
        """Custom paint event to draw the three dots with a unique style"""
        super().paintEvent(event)
        
        from PySide6.QtGui import QBrush, QColor, QPainter, QPen
        
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # Define colors
        primary_color = QColor("#4285F4")  # Google Blue
        secondary_color = QColor("#34A853")  # Google Green
        tertiary_color = QColor("#FBBC05")  # Google Yellow
        
        # Calculate positions
        center_x = self.width() / 2
        dot_size = 4
        spacing = 5
        
        # Draw the three dots in a vertical arrangement with different colors
        y_positions = [self.height() / 2 - spacing, self.height() / 2, self.height() / 2 + spacing]
        colors = [primary_color, secondary_color, tertiary_color]
        
        for i, (y, color) in enumerate(zip(y_positions, colors)):
            painter.setPen(QPen(color, 1))
            painter.setBrush(QBrush(color))
            painter.drawEllipse(int(center_x - dot_size/2), int(y - dot_size/2), dot_size, dot_size)

def main():
    """Main application entry point"""
    # Enable high DPI scaling
    if hasattr(Qt, 'HighDpiScaleFactorRoundingPolicy'):
        QApplication.setHighDpiScaleFactorRoundingPolicy(
            Qt.HighDpiScaleFactorRoundingPolicy.PassThrough)
    
    app = QApplication(sys.argv)
    app.setApplicationName("MyBro")
    app.setOrganizationName("MyBro")
    
    # Set application style
    app.setStyle("Fusion")
    
    # Log screen information directly here instead of using a method
    screens = QGuiApplication.screens()
    primary_screen = QGuiApplication.primaryScreen()
    
    logger.info(f"Total screens: {len(screens)}")
    if primary_screen:
        logger.info(f"Primary screen: {primary_screen.name()}")
    
    for i, screen in enumerate(screens):
        is_primary = "PRIMARY" if screen == primary_screen else ""
        logger.info(f"Screen {i}: {screen.name()} {is_primary}")
        logger.info(f"  - Size: {screen.size().width()}x{screen.size().height()}")
        logger.info(f"  - Available geometry: {screen.availableGeometry().width()}x{screen.availableGeometry().height()}")
    
    # Create and show the browser
    browser = MyBrowser()
    browser.show()
    
    # Start the application
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
